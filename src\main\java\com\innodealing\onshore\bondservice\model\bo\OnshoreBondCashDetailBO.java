package com.innodealing.onshore.bondservice.model.bo;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 国内债券现金流聚合表表实体对象
 *
 * <AUTHOR>
 */
public class OnshoreBondCashDetailBO {
    /**
     * 发行人唯一代码
     */
    private Long comUniCode;
    /**
     * 债券唯一代码
     */
    private Long bondUniCode;
    /**
     * 数据日期
     */
    private Date dataDate;
    /**
     * 数据年份
     */
    private Integer dataYear;
    /**
     * 数据月份
     */
    private Integer dataMonth;
    /**
     * 季度(1,2,3,4: 代表4个季度), 对应枚举QuarterEnum
     */
    private Integer dataQuarter;
    /**
     * 偿付利息(万)
     */
    private BigDecimal payInterestCash;
    /**
     * 偿付本金(万)
     */
    private BigDecimal payPrincipalCash;
    /**
     * 含权偿付利息(万)
     */
    private BigDecimal exercisePayInterestCash;
    /**
     * 含权偿付本金(万)
     */
    private BigDecimal exercisePayPrincipalCash;
    /**
     * 理论付息日(万)
     */
    private Date theoryInterestDate;


    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }


    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }


    public Date getDataDate() {
        return dataDate;
    }

    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }


    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }


    public Integer getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(Integer dataMonth) {
        this.dataMonth = dataMonth;
    }


    public Integer getDataQuarter() {
        return dataQuarter;
    }

    public void setDataQuarter(Integer dataQuarter) {
        this.dataQuarter = dataQuarter;
    }


    public BigDecimal getPayInterestCash() {
        return payInterestCash;
    }

    public void setPayInterestCash(BigDecimal payInterestCash) {
        this.payInterestCash = payInterestCash;
    }


    public BigDecimal getPayPrincipalCash() {
        return payPrincipalCash;
    }

    public void setPayPrincipalCash(BigDecimal payPrincipalCash) {
        this.payPrincipalCash = payPrincipalCash;
    }


    public BigDecimal getExercisePayInterestCash() {
        return exercisePayInterestCash;
    }

    public void setExercisePayInterestCash(BigDecimal exercisePayInterestCash) {
        this.exercisePayInterestCash = exercisePayInterestCash;
    }


    public BigDecimal getExercisePayPrincipalCash() {
        return exercisePayPrincipalCash;
    }

    public void setExercisePayPrincipalCash(BigDecimal exercisePayPrincipalCash) {
        this.exercisePayPrincipalCash = exercisePayPrincipalCash;
    }

    public Date getTheoryInterestDate() {
        return theoryInterestDate;
    }

    public void setTheoryInterestDate(Date theoryInterestDate) {
        this.theoryInterestDate = theoryInterestDate;
    }
}

