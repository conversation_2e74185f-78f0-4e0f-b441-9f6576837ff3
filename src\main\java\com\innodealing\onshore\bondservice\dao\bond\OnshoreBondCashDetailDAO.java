package com.innodealing.onshore.bondservice.dao.bond;

import com.github.wz2cool.dynamic.DynamicQuery;
import com.github.wz2cool.dynamic.FilterGroupDescriptor;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondservice.config.datasource.BondDataSourceConfig;
import com.innodealing.onshore.bondservice.mapper.bond.OnshoreBondCashDetailMapper;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondCashDetailDO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 国内债券现金流水明细表表数据库访问层 {@link OnshoreBondCashDetailDO}
 * 对OnshoreBondCashDetailMapper层做出简单封装 {@link OnshoreBondCashDetailMapper}
 *
 * <AUTHOR>
 */
@Repository
public class OnshoreBondCashDetailDAO {

    @Resource
    private OnshoreBondCashDetailMapper onshoreBondCashDetailMapper;
    @Resource(name = BondDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;



    /**
     * 批量插入或更新
     *
     * @param onshoreBondCashDetails List<OnshoreBondCashDetailDO>
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = BondDataSourceConfig.TRANSACTION_NAME)
    public int batchUpsert(List<OnshoreBondCashDetailDO> onshoreBondCashDetails) {
        if (CollectionUtils.isEmpty(onshoreBondCashDetails)) {
            return 0;
        }
        FilterGroupDescriptor<OnshoreBondCashDetailDO> filterGroup = new FilterGroupDescriptor<>();
        for (OnshoreBondCashDetailDO onshoreBondCashDetailDO : onshoreBondCashDetails) {
            FilterGroupDescriptor<OnshoreBondCashDetailDO> innerFilterGroup = new FilterGroupDescriptor<>();
            innerFilterGroup.and(OnshoreBondCashDetailDO::getComUniCode, v -> v.isEqual(onshoreBondCashDetailDO.getComUniCode()))
                    .and(OnshoreBondCashDetailDO::getBondUniCode, v -> v.isEqual(onshoreBondCashDetailDO.getBondUniCode()))
                    .and(OnshoreBondCashDetailDO::getDataDate, v -> v.isEqual(onshoreBondCashDetailDO.getDataDate()));
            filterGroup.or(innerFilterGroup);
        }
        DynamicQuery<OnshoreBondCashDetailDO> query = DynamicQuery.createQuery(OnshoreBondCashDetailDO.class).and(filterGroup);
        List<OnshoreBondCashDetailDO> olds = onshoreBondCashDetailMapper.selectByDynamicQuery(query);
        Map<String, OnshoreBondCashDetailDO> oldMap = olds.stream()
                .collect(Collectors.toMap(this::getUniqueKey, Function.identity(), (v1, v2) -> v2));
        Set<String> keys = oldMap.keySet();
        List<OnshoreBondCashDetailDO> insertList = new ArrayList<>();
        List<OnshoreBondCashDetailDO> updateList = new ArrayList<>();
        for (OnshoreBondCashDetailDO entity : onshoreBondCashDetails) {
            String key = this.getUniqueKey(entity);
            if (keys.contains(key)) {
                entity.setId(oldMap.get(key).getId());
                updateList.add(entity);
            } else {

                insertList.add(entity);
            }
        }
        return this.batchInsertSelective(insertList) + this.batchUpdateByIdSelective(updateList);
    }


    /**
     * 批量插入
     *
     * @param assetDayNewestQuotes List<OnshoreBondCashDetailDO>
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = BondDataSourceConfig.TRANSACTION_NAME)
    public int batchInsertSelective(List<OnshoreBondCashDetailDO> assetDayNewestQuotes) {
        if (CollectionUtils.isEmpty(assetDayNewestQuotes)) {
            return 0;
        }
        MapperBatchAction<OnshoreBondCashDetailMapper> insertBatchAction = MapperBatchAction.create(OnshoreBondCashDetailMapper.class, sqlSessionFactory);
        for (OnshoreBondCashDetailDO entity : assetDayNewestQuotes) {
            insertBatchAction.addAction(mapper -> mapper.insertSelective(entity));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 批量更新
     *
     * @param assetDayNewestQuotes List<OnshoreBondCashDetailDO>
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = BondDataSourceConfig.TRANSACTION_NAME)
    public int batchUpdateByIdSelective(List<OnshoreBondCashDetailDO> assetDayNewestQuotes) {
        if (CollectionUtils.isEmpty(assetDayNewestQuotes)) {
            return 0;
        }
        MapperBatchAction<OnshoreBondCashDetailMapper> insertBatchAction = MapperBatchAction.create(OnshoreBondCashDetailMapper.class, sqlSessionFactory);
        for (OnshoreBondCashDetailDO entity : assetDayNewestQuotes) {
            insertBatchAction.addAction(mapper -> mapper.updateByPrimaryKeySelective(entity));
        }
        return insertBatchAction.doBatchActions();
    }


    /**
     * 获取unique主键
     *
     * @param comUniCode  主体编码
     * @param bondUniCode 债券唯一编码
     * @param dataYear    数据year
     * @param dataMonth   数据month
     * @return {@link String}
     */
    public String getUniqueKey(Long comUniCode, Long bondUniCode, Integer dataYear, Integer dataMonth) {
        return comUniCode + "_" + bondUniCode + "_" + dataYear + "_" + dataMonth;
    }

    /**
     * 获取unique主键
     *
     * @param cashDetail 现金流明细
     * @return {@link String}
     */
    public String getUniqueKey(OnshoreBondCashDetailDO cashDetail) {
        return cashDetail.getComUniCode() + "_" + cashDetail.getBondUniCode() + "_" + cashDetail.getDataDate();
    }
}
