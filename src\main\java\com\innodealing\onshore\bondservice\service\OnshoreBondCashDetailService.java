package com.innodealing.onshore.bondservice.service;

import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondCashDetailDO;

import java.util.Set;


/**
 * 国内债券现金流水明细表表Service层 {@link OnshoreBondCashDetailDO}
 *
 * <AUTHOR>
 */
public interface OnshoreBondCashDetailService {

    /**
     * 同步债券同步明细
     *
     * @param comUniCodes 主体编码集合
     */
    void syncOnshoreBondCashDetail(Set<Long> comUniCodes);

    /**
     * 同步单个债券同步明细
     *
     * @param comUniCode  主体编码
     * @param bondUniCode 债券编码
     */
    void handleSingleOnshoreBondCashDetail(Long comUniCode, Long bondUniCode);

    /**
     * job同步债券同步明细
     *
     */
    void jobSyncOnshoreBondCashDetail();

    /**
     * 刷新物化视图
     */
    void refreshMaterializedView();

    /**
     * 删除所有数据
     */
    void deleteAllOnshoreBondCashDetail();
}
