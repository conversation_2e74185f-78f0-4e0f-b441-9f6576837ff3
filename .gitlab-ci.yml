image: prd-container-registry-registry.cn-shanghai.cr.aliyuncs.com/public/maven3-aliyun-jdk8:3

cache:
  paths:
    - .m2/

variables:
  MAVEN_OPTS: "-Dmaven.repo.local=.m2"

stages:
  - build

job_build:
  variables:
    # 比较的分支,默认为master,如果改动比较大,建议写成你的release分支
    TARGET_BRANCH: "master"
    HOST_URL: "http://*************:9000"
    LOGIN_TOKEN: "8010098c44f4f9f8c280247bfed705d6d084fe17"
  stage: build
  script:
    - java Ci
  tags:
    - build
