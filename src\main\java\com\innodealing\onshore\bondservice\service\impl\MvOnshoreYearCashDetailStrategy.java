package com.innodealing.onshore.bondservice.service.impl;

import com.innodealing.onshore.bondservice.dao.pgbond.mv.MvOnshoreBondCashDetailYearDAO;
import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailYearGroupDO;
import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;
import com.innodealing.onshore.bondservice.service.MvOnshoreBondCashDetailStrategy;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 年度数据查询策略实现
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@Component
public class MvOnshoreYearCashDetailStrategy implements MvOnshoreBondCashDetailStrategy {

    @Resource
    private MvOnshoreBondCashDetailYearDAO mvOnshoreBondCashDetailYearDAO;

    @Override
    public DateTypeEnum getSupportedDateType() {
        return DateTypeEnum.YEAR;
    }

    @Override
    public List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataNoExercise(Set<Long> comUniCodes) {
        List<MvOnshoreBondCashDetailYearGroupDO> mvYearData =
                mvOnshoreBondCashDetailYearDAO.listMvOnshoreBondCashDetailYearGroupDO(comUniCodes);
        return convertToBondCashPayInfoDTO(mvYearData);
    }

    @Override
    public List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataExercise(Set<Long> comUniCodes) {
        List<MvOnshoreBondCashDetailYearGroupDO> mvYearData =
                mvOnshoreBondCashDetailYearDAO.listMvOnshoreBondCashDetailYearGroupDO(comUniCodes);
        return convertToExerciseBondCashPayInfoDTO(mvYearData);
    }


    /**
     * 转换为普通债券现金流DTO
     */
    private List<BondCashPayInfoDTO> convertToBondCashPayInfoDTO(List<MvOnshoreBondCashDetailYearGroupDO> dataList) {
        return dataList.stream().map(data -> {
            BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
            dto.setDataYear(data.getDataYear());
            dto.setDataDateStr(data.getDataYear() + Strings.EMPTY);
            dto.setPayInterestCash(data.getTotalPayInterestCash());
            dto.setPayPrincipalCash(data.getTotalPayPrincipalCash());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为行权债券现金流DTO
     */
    private List<BondCashPayInfoDTO> convertToExerciseBondCashPayInfoDTO(List<MvOnshoreBondCashDetailYearGroupDO> dataList) {
        return dataList.stream().map(data -> {
            BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
            dto.setDataYear(data.getDataYear());
            dto.setDataDateStr(data.getDataYear() + Strings.EMPTY);
            dto.setPayInterestCash(data.getTotalExercisePayInterestCash());
            dto.setPayPrincipalCash(data.getTotalExercisePayPrincipalCash());
            return dto;
        }).collect(Collectors.toList());
    }
} 