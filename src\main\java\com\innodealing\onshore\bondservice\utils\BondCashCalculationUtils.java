package com.innodealing.onshore.bondservice.utils;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.HUNDRED;

/**
 * 债券现金流计算工具类
 * 封装重复的计算逻辑，提供通用的计算方法
 *
 * <AUTHOR>
 */
public class BondCashCalculationUtils {

    private static final Logger logger = LoggerFactory.getLogger(BondCashCalculationUtils.class);

    /**
     * 按年月分组债券规模数据
     *
     * @param bondAmounts 债券规模数据列表
     * @param keyGenerator 分组键生成器
     * @return 分组后的数据
     */
    public static Map<String, List<BondAmountDTO>> groupBondAmountsByKey(List<BondAmountDTO> bondAmounts, 
                                                                         Function<BondAmountDTO, String> keyGenerator) {
        return bondAmounts.stream().collect(Collectors.groupingBy(keyGenerator));
    }

    /**
     * 按年月分组债券现金流数据
     *
     * @param cashFlows 现金流数据列表
     * @param keyGenerator 分组键生成器
     * @return 分组后的数据
     */
    public static Map<String, List<BondCashFlowViewDTO>> groupBondCashFlowsByKey(List<BondCashFlowViewDTO> cashFlows,
                                                                                 Function<BondCashFlowViewDTO, String> keyGenerator) {
        return cashFlows.stream()
                .filter(cash -> Objects.nonNull(cash.getInterestEndDate()))
                .collect(Collectors.groupingBy(keyGenerator));
    }

    /**
     * 过滤历史现金流数据（小于今天）
     *
     * @param cashFlows 现金流数据列表
     * @param keyGenerator 分组键生成器
     * @return 过滤后的分组数据
     */
    public static Map<String, List<BondCashFlowViewDTO>> filterHistoricalCashFlows(List<BondCashFlowViewDTO> cashFlows,
                                                                                    Function<BondCashFlowViewDTO, String> keyGenerator) {
        return cashFlows.stream()
                .filter(cash -> Objects.nonNull(cash.getInterestEndDate()))
                .filter(v -> LocalDate.now().isAfter(v.getInterestEndDate().toLocalDate()))
                .collect(Collectors.groupingBy(keyGenerator));
    }

    /**
     * 计算利息现金流
     * 公式：利息支付 * 剩余规模 / 100
     *
     * @param cashList 现金流列表
     * @param latestRemainAmount 最新剩余规模
     * @return 计算后的利息现金流
     */
    public static Optional<BigDecimal> calculateInterestCash(List<BondCashFlowViewDTO> cashList, BigDecimal latestRemainAmount) {
        List<BigDecimal> interestCashList = cashList.stream()
                .map(BondCashFlowViewDTO::getInterestPayment)
                .map(v -> BigDecimalUtils.safeMultiply(v, latestRemainAmount)
                        .flatMap(k -> BigDecimalUtils.safeDivide(k, HUNDRED, RoundingMode.HALF_UP)).orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return BigDecimalUtils.safeAdd(interestCashList);
    }

    /**
     * 计算本金现金流
     * 对变动金额进行累加
     *
     * @param monthlyAmounts 月度金额数据
     * @return 计算后的本金现金流
     */
    public static Optional<BigDecimal> calculatePrincipalCash(List<BondAmountDTO> monthlyAmounts) {
        return BigDecimalUtils.safeAdd(monthlyAmounts.stream()
                .map(BondAmountDTO::getChangeAmount)
                .collect(Collectors.toList()));
    }

    /**
     * 处理不含权债券的利息计算通用逻辑
     *
     * @param context 上下文
     * @param bondUniCode 债券唯一编码
     * @param sameYearMonthAmountDataList 按年月分组的金额数据
     * @param sameYearMonthBondCashFlowDataList 按年月分组的现金流数据
     * @param cashDetailSetter 现金流详情设置器
     */
    public static void processNonRightBondInterest(BondProcessContext context, Long bondUniCode,
                                                   Map<String, List<BondAmountDTO>> sameYearMonthAmountDataList,
                                                   Map<String, List<BondCashFlowViewDTO>> sameYearMonthBondCashFlowDataList,
                                                   BiConsumer<OnshoreBondCashDetailBO, BigDecimal> cashDetailSetter) {
        // 处理每个月的数据
        for (List<BondCashFlowViewDTO> cashList : sameYearMonthBondCashFlowDataList.values()) {
            Date date = cashList.stream().findFirst().map(BondCashFlowViewDTO::getInterestEndDate).orElse(null);
            if (cashList.isEmpty() || Objects.isNull(date)) {
                continue;
            }
            Date exerciseDate = Date.valueOf(date.toLocalDate().plusDays(1L));
            
            // 查找最新剩余规模
            Optional<BondAmountDTO> filterBondAmountOpt = sameYearMonthAmountDataList.values().stream()
                    .flatMap(List::stream)
                    .filter(v -> Objects.nonNull(v.getChangeDate()) && v.getChangeDate().before(date))
                    .max(Comparator.comparing(BondAmountDTO::getChangeDate)
                            .thenComparing(BondAmountDTO::getRemainAmount, Comparator.reverseOrder()));
            
            if (!filterBondAmountOpt.isPresent() || Objects.isNull(filterBondAmountOpt.get().getRemainAmount())) {
                logger.warn("processNonRightBondInterest 最新剩余规模不存在,跳过：com_uni_code:{},bond_uni_code:{}",
                        context.getComUniCode(), bondUniCode);
                continue;
            }
            
            OnshoreBondCashDetailBO cashDetail = context.getOrCreateCashDetail(bondUniCode, exerciseDate);
            BigDecimal latestRemainAmount = filterBondAmountOpt.get().getRemainAmount();
            
            // 计算利息现金流
            calculateInterestCash(cashList, latestRemainAmount).ifPresent(amount -> cashDetailSetter.accept(cashDetail, amount));
        }
    }

    /**
     * 处理不含权债券的本金计算通用逻辑
     *
     * @param context 上下文
     * @param bondUniCode 债券唯一编码
     * @param sameMonthDataList 按月分组的数据
     * @param cashDetailSetter 现金流详情设置器
     */
    public static void processNonRightBondPrincipal(BondProcessContext context, Long bondUniCode,
                                                    Map<String, List<BondAmountDTO>> sameMonthDataList,
                                                    BiConsumer<OnshoreBondCashDetailBO, BigDecimal> cashDetailSetter) {
        // 处理每个月的数据
        for (List<BondAmountDTO> monthlyAmounts : sameMonthDataList.values()) {
            Date date = monthlyAmounts.stream().findFirst().map(BondAmountDTO::getChangeDate).orElse(null);
            if (monthlyAmounts.isEmpty() || Objects.isNull(date)) {
                continue;
            }
            OnshoreBondCashDetailBO cashDetail = context.getOrCreateCashDetail(bondUniCode, date);
            calculatePrincipalCash(monthlyAmounts).ifPresent(amount -> cashDetailSetter.accept(cashDetail, amount));
        }
    }

    /**
     * 处理含权债券历史数据的本金计算
     *
     * @param context 上下文
     * @param bondUniCode 债券唯一编码
     * @param bondAmounts 债券金额数据
     * @param filterChangeReasons 过滤的变动原因
     * @param keyGenerator 分组键生成器
     * @param cashDetailSetter 现金流详情设置器
     */
    public static void processRightBondHistoricalPrincipal(BondProcessContext context, Long bondUniCode,
                                                           List<BondAmountDTO> bondAmounts,
                                                           Set<Integer> filterChangeReasons,
                                                           Function<BondAmountDTO, String> keyGenerator,
                                                           BiConsumer<OnshoreBondCashDetailBO, BigDecimal> cashDetailSetter) {
        Map<String, List<BondAmountDTO>> sameMonthDataList = bondAmounts.stream()
                .filter(v -> Objects.nonNull(v.getChangeDate()))
                .filter(bondAmount -> filterChangeReasons.contains(bondAmount.getChangeReasonCode()))
                .filter(v -> LocalDate.now().isAfter(v.getChangeDate().toLocalDate()))
                .collect(Collectors.groupingBy(keyGenerator));

        processNonRightBondPrincipal(context, bondUniCode, sameMonthDataList, cashDetailSetter);
    }

    /**
     * 验证必要的数据是否存在
     *
     * @param context 上下文
     * @param bondUniCode 债券唯一编码
     * @param methodName 方法名（用于日志）
     * @return 是否通过验证
     */
    public static boolean validateContextData(BondProcessContext context, Long bondUniCode, String methodName) {
        if (Objects.isNull(context)) {
            logger.warn("{} 上下文为空,跳过：bond_uni_code:{}", methodName, bondUniCode);
            return false;
        }
        if (Objects.isNull(bondUniCode)) {
            logger.warn("{} 债券编码为空,跳过", methodName);
            return false;
        }
        return true;
    }
} 