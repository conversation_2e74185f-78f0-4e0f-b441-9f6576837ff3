package com.innodealing.onshore.bondservice.config.constant;

/**
 * 字符串常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @Description 字符串常量
 * @createTime 2025年04月29日 15:46:00
 */
public final class StringConstant{

    private StringConstant() {}

    /**
     * 票据-互联网通道标识
     */
    public static final String BILL_INTERNET_LINE = "internet";
    /**
     * 票据-专线通道标识
     */
    public static final String BILL_SPECIAL_LINE = "special";

    /**
     * bill-client-apollo 客户端接收时间
     */
    public static final String CLIENT_RECEIVE_TIME_HEADER_KEY = "dm-client-receive-time";
    /**
     * bill-client-apollo 发送时间
     */
    public static final String CLIENT_SEND_TIME_HEADER_KEY = "dm-client-send-time";

}
