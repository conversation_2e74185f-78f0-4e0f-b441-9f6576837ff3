package com.innodealing.onshore.bondservice.service.processor.calculator;

import com.google.common.collect.ImmutableSet;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.enums.ChangeReasonEnum;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Date;
import java.time.LocalDate;
import java.util.*;

/**
 * 现金流明细计算器抽象基类
 * 提供通用的计算逻辑和工具方法，使用模板方法模式+钩子方法
 *
 * <AUTHOR>
 */
public abstract class AbstractBondCashDetailCalculator implements BondCashDetailCalculator {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    // 公共常量：变动原因过滤条件 2,3,7,8,9,10,12
    protected static final Set<Integer> FILTER_CHANGE_REASONS = ImmutableSet.of(
            ChangeReasonEnum.SELL_BACK.getValue(), ChangeReasonEnum.REDEMPTION.getValue(), 
            ChangeReasonEnum.MATURITY_PAYMENT.getValue(), ChangeReasonEnum.EARLY_REPAYMENT_PRINCIPAL.getValue(), 
            ChangeReasonEnum.OVERHAND_REPAYMENT.getValue(), ChangeReasonEnum.FIX_REPAYMENT.getValue(),
            ChangeReasonEnum.TREASURY_REPURCHASE.getValue()
    );

    @Override
    public final void calculate(BondProcessContext context, Long bondUniCode) {
        // 前置处理钩子
        BondProcessContext processedContext = before(context, bondUniCode);
        // 执行具体的计算逻辑
        this.doCalculate(processedContext, bondUniCode);
        // 后置处理钩子
        after(processedContext, bondUniCode);
    }

    /**
     * 前置处理钩子方法
     * 子类可以重写此方法进行预处理，比如数据验证、预计算等
     *
     * @param context 处理上下文
     * @param bondUniCode 债券唯一编码
     * @return 处理后的上下文
     */
    protected BondProcessContext before(BondProcessContext context, Long bondUniCode) {
        // 默认实现：直接返回原context，子类可重写
        return context;
    }

    /**
     * 后置处理钩子方法
     * 子类可以重写此方法进行后处理，比如结果验证、清理等
     *
     * @param context 处理上下文
     * @param bondUniCode 债券唯一编码
     */
    protected void after(BondProcessContext context, Long bondUniCode) {
        // 默认实现：空操作，子类可重写
    }

    /**
     * 执行具体的计算逻辑
     * 子类需要实现此方法
     *
     * @param context 处理上下文
     * @param bondUniCode 债券唯一编码
     */
    protected abstract void doCalculate(BondProcessContext context, Long bondUniCode);

    /**
     * 生成债券规模变动数据的分组键
     *
     * @param bondAmount 债券规模变动数据
     * @return 分组键 (bondUniCode_year_month)
     */
    protected String getBondAmountKey(BondAmountDTO bondAmount) {
        if (Objects.isNull(bondAmount) || Objects.isNull(bondAmount.getChangeDate())) {
            return Strings.EMPTY;
        }
        LocalDate localDate = bondAmount.getChangeDate().toLocalDate();
        return String.format("%s_%s_%s", bondAmount.getBondUniCode(), localDate.getYear(), localDate.getMonth().getValue());
    }

    /**
     * 生成债券规模现金流数据的分组键
     *
     * @param bondCashFlowView 债券规模现金流数据
     * @return 分组键 (bondUniCode_year_month)
     */
    protected String getBondCashFlowKey(BondCashFlowViewDTO bondCashFlowView) {
        if (Objects.isNull(bondCashFlowView) || Objects.isNull(bondCashFlowView.getInterestEndDate())) {
            return Strings.EMPTY;
        }
        LocalDate localDate = bondCashFlowView.getInterestEndDate().toLocalDate();
        return String.format("%s_%s_%s", bondCashFlowView.getBondUniCode(), localDate.getYear(), localDate.getMonth().getValue());
    }

    /**
     * 过滤出行权周期=1的行权现金流数据
     * 提取的公共方法，避免重复代码
     *
     * @param context     上下文
     * @param bondUniCode 债券唯一编码
     * @return {@link Optional }<{@link BondCashFlowViewDTO }>
     */
    protected Optional<BondCashFlowViewDTO> filterExerciseBondCashFlow(BondProcessContext context, Long bondUniCode) {
        // 过滤出大于今天的数据，并取最小结息日
        return context.getBondCashFlowMap().getOrDefault(bondUniCode, Collections.emptyList()).stream()
                .filter(cash -> Objects.nonNull(cash.getInterestEndDate()))
                // 过滤出大于今天的数据，并取最小结息日
                .filter(cash -> cash.getInterestEndDate().after(Date.valueOf(LocalDate.now())))
                // 过滤出行权数据行权周期=1
                .filter(cash -> Objects.equals(cash.getOptionPeriodFlag(), 1))
                .min(Comparator.comparing(BondCashFlowViewDTO::getInterestEndDate));
    }



    /**
     * 查找最新的剩余规模
     * 满足在指定日期前最大的change_date对应的bond_balance作为最新剩余规模
     * 如最大change_date有多条，那么应该取bond_balance最小的作为最新剩余规模
     *
     * @param context 上下文
     * @param bondUniCode 债券唯一编码
     * @param beforeDate 截止日期
     * @return 最新剩余规模数据
     */
    protected Optional<BondAmountDTO> findLatestRemainAmount(BondProcessContext context, Long bondUniCode, Date beforeDate) {
        return context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList())
                .stream()
                .filter(v -> Objects.nonNull(v.getChangeDate()) && v.getChangeDate().before(beforeDate))
                .max(Comparator.comparing(BondAmountDTO::getChangeDate)
                        .thenComparing(BondAmountDTO::getRemainAmount, Comparator.reverseOrder()));
    }

    /**
     * 检查是否为含权债券
     *
     * @param bondOptionStatus 债券含权状态
     * @return true if 含权债券
     */
    protected boolean isEmbeddedOptionBond(BondOptionStatusDTO bondOptionStatus) {
        return isCallable(bondOptionStatus) || isPutable(bondOptionStatus) ;
    }

    /**
     * 检查是否可赎回
     */
    protected boolean isCouponAdjustableFlag(BondOptionStatusDTO bondOptionStatus) {
        return bondOptionStatus != null &&
                bondOptionStatus.getCouponAdjustableFlag() != null &&
                bondOptionStatus.getCouponAdjustableFlag();
    }

    /**
     * 检查是否可赎回
     */
    protected boolean isCallable(BondOptionStatusDTO bondOptionStatus) {
        return bondOptionStatus != null &&
                bondOptionStatus.getCallableFlag() != null &&
                bondOptionStatus.getCallableFlag();
    }

    /**
     * 检查是否可回售
     */
    protected boolean isPutable(BondOptionStatusDTO bondOptionStatus) {
        return bondOptionStatus != null &&
                bondOptionStatus.getPutableFlag() != null &&
                bondOptionStatus.getPutableFlag();
    }
}
