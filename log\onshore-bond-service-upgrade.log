【1/6 git clone】Cloning into directory: C:\Users\<USER>\AppData\Local\Temp\git-clone-7430655494926606041\onshore-bond-service
【2/6 mvn package】C:\Program Files\JetBrains\IntelliJ IDEA 2022.2.1\plugins\maven\lib\maven3\bin\mvn.cmd clean package -DskipTests=true
【2/6 mvn package】[INFO] Scanning for projects...
【2/6 mvn package】[INFO] ------------------------------------------------------------------------
【2/6 mvn package】[INFO] BUILD FAILURE
【2/6 mvn package】[INFO] ------------------------------------------------------------------------
【2/6 mvn package】[INFO] Total time:  0.068 s
【2/6 mvn package】[INFO] Finished at: 2025-06-30T14:53:22+08:00
【2/6 mvn package】[INFO] ------------------------------------------------------------------------
【2/6 mvn package】[ERROR] The goal you specified requires a project to execute but there is no POM in this directory (C:\Users\<USER>\AppData\Local\Temp\git-clone-7430655494926606041\onshore-bond-service). Please verify you invoked Maven from the correct directory. -> [Help 1]
【2/6 mvn package】[ERROR] 
【2/6 mvn package】[ERROR] To see the full stack trace of the errors, re-run Maven with the -e switch.
【2/6 mvn package】[ERROR] Re-run Maven using the -X switch to enable full debug logging.
【2/6 mvn package】[ERROR] 
【2/6 mvn package】[ERROR] For more information about the errors and possible solutions, please read the following articles:
【2/6 mvn package】[ERROR] [Help 1] http://cwiki.apache.org/confluence/display/MAVEN/MissingProjectException
