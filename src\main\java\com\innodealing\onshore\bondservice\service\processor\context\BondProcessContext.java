package com.innodealing.onshore.bondservice.service.processor.context;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.enums.QuarterEnum;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;

import javax.annotation.Nonnull;
import java.sql.Date;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 债券处理上下文
 * 用于在处理过程中传递数据和状态
 *
 * <AUTHOR>
 */
public class BondProcessContext {

    /**
     * 主体唯一编码
     */
    private Long comUniCode;

    /**
     * 债券唯一编码列表
     */
    private List<Long> bondUniCodes;

    /**
     * 债券基础信息
     */
    private Map<Long, OnshoreBondInfoDTO> onshoreBondInfoDTOMap;

    /**
     * 债券规模变动信息
     * Key: bondUniCode, Value: BondAmountDTO列表
     */
    private Map<Long, List<BondAmountDTO>> bondAmountMap;

    /**
     * 债券含权信息
     * Key: bondUniCode, Value: BondOptionStatusDTO
     */
    private Map<Long, BondOptionStatusDTO> bondOptionStatusMap;

    /**
     * 债券现金流信息
     * Key: bondUniCode, Value: BondCashFlowViewDTO列表
     */
    private Map<Long, List<BondCashFlowViewDTO>> bondCashFlowMap;

    /**
     * 处理结果
     * Key: 唯一键(comUniCode_bondUniCode_dataYear_dataMonth), Value: OnshoreBondCashDetailBO
     */
    private Map<String, OnshoreBondCashDetailBO> resultMap;

    /**
     * 构造函数
     */
    public BondProcessContext() {
        this.bondUniCodes = new ArrayList<>();
        this.bondAmountMap = new HashMap<>();
        this.bondOptionStatusMap = new HashMap<>();
        this.bondCashFlowMap = new HashMap<>();
        this.onshoreBondInfoDTOMap = new HashMap<>();
        this.resultMap = new ConcurrentHashMap<>();
    }

    /**
     * 获取或创建债券现金流明细对象
     *
     * @param bondUniCode 债券唯一编码
     * @param dataDate    数据date
     * @return OnshoreBondCashDetailBO
     */
    @Nonnull
    public OnshoreBondCashDetailBO getOrCreateCashDetail(@Nonnull Long bondUniCode, @Nonnull Date dataDate) {
        LocalDate localDate = dataDate.toLocalDate();
        String uniqueKey = buildUniqueKey(comUniCode, bondUniCode, localDate.getYear(), localDate.getMonthValue());
        return resultMap.computeIfAbsent(uniqueKey, k -> {
            OnshoreBondCashDetailBO cashDetail = new OnshoreBondCashDetailBO();
            cashDetail.setComUniCode(comUniCode);
            cashDetail.setBondUniCode(bondUniCode);
            cashDetail.setDataYear(localDate.getYear());
            cashDetail.setDataMonth(localDate.getMonthValue());
            // 设置季度
            cashDetail.setDataQuarter(QuarterEnum.getSeason(dataDate).getValue());
            // 设置数据日期（月初）
            cashDetail.setDataDate(Date.valueOf(LocalDate.of(localDate.getYear(), localDate.getMonthValue(), 1)));
            return cashDetail;
        });
    }

    /**
     * 获取处理结果列表
     */
    public List<OnshoreBondCashDetailBO> getResults() {
        return new ArrayList<>(resultMap.values());
    }

    /**
     * 构建唯一键
     */
    private String buildUniqueKey(Long comUniCode, Long bondUniCode, Integer dataYear, Integer dataMonth) {
        return comUniCode + "_" + bondUniCode + "_" + dataYear + "_" + dataMonth;
    }

    // Getter and Setter methods
    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public List<Long> getBondUniCodes() {
        return bondUniCodes;
    }

    public void setBondUniCodes(List<Long> bondUniCodes) {
        this.bondUniCodes = bondUniCodes;
    }

    public Map<Long, List<BondAmountDTO>> getBondAmountMap() {
        return bondAmountMap;
    }

    public void setBondAmountMap(Map<Long, List<BondAmountDTO>> bondAmountMap) {
        this.bondAmountMap = bondAmountMap;
    }

    public Map<Long, BondOptionStatusDTO> getBondOptionStatusMap() {
        return bondOptionStatusMap;
    }

    public void setBondOptionStatusMap(Map<Long, BondOptionStatusDTO> bondOptionStatusMap) {
        this.bondOptionStatusMap = bondOptionStatusMap;
    }

    public Map<Long, List<BondCashFlowViewDTO>> getBondCashFlowMap() {
        return bondCashFlowMap;
    }

    public void setBondCashFlowMap(Map<Long, List<BondCashFlowViewDTO>> bondCashFlowMap) {
        this.bondCashFlowMap = bondCashFlowMap;
    }

    public Map<String, OnshoreBondCashDetailBO> getResultMap() {
        return resultMap;
    }

    public void setResultMap(Map<String, OnshoreBondCashDetailBO> resultMap) {
        this.resultMap = resultMap;
    }

    public Map<Long, OnshoreBondInfoDTO> getOnshoreBondInfoDTOMap() {
        return onshoreBondInfoDTOMap;
    }

    public void setOnshoreBondInfoDTOMap(Map<Long, OnshoreBondInfoDTO> onshoreBondInfoDTOMap) {
        this.onshoreBondInfoDTOMap = onshoreBondInfoDTOMap;
    }
}
