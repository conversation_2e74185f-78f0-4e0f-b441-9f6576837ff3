package com.innodealing.onshore.bondservice.dao.pgbond.mv;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.innodealing.onshore.bondservice.mapper.pgbond.mv.MvOnshoreBondCashDetailQuarterMapper;
import com.innodealing.onshore.bondservice.mapper.pgbond.mv.group.MvOnshoreBondCashDetailQuarterGroupMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailQuarterGroupDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.mv.MvOnshoreBondCashDetailQuarterDO;
import com.innodealing.onshore.dao.postgresql.table.PgOperationTemplate;
import com.innodealing.onshore.service.AliasRefreshMaterializedView;
import com.sun.istack.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 国内债券现金流按季度统计物化视图
 *
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2024年10月22日 20:43:00
 */
@Repository
public class MvOnshoreBondCashDetailQuarterDAO extends AliasRefreshMaterializedView {

    @Resource
    private MvOnshoreBondCashDetailQuarterMapper mvOnshoreBondCashDetailQuarterMapper;
    @Resource
    private MvOnshoreBondCashDetailQuarterGroupMapper mvOnshoreBondCashDetailQuarterGroupMapper;

    /**
     * 实例化
     *
     * @param pgOperationTemplate 模板类
     * @author: 张飞翔
     * @date: 2023/8/21 13:59
     */
    public MvOnshoreBondCashDetailQuarterDAO(@Autowired PgOperationTemplate pgOperationTemplate) {
        super(MvOnshoreBondCashDetailQuarterDO.class, pgOperationTemplate);
    }

    /**
     * 国内债券现金流按月统计物化视图
     *
     * @param comUniCodes 发行人编码
     * @return 现金流统计数据
     */

    public List<MvOnshoreBondCashDetailQuarterGroupDO> listMvOnshoreBondCashDetailQuarterGroupDO(@NotNull Collection<Long> comUniCodes) {
        GroupedQuery<MvOnshoreBondCashDetailQuarterDO, MvOnshoreBondCashDetailQuarterGroupDO> groupedQuery =
                GroupByQuery.createQuery(MvOnshoreBondCashDetailQuarterDO.class, MvOnshoreBondCashDetailQuarterGroupDO.class)
                        .and(Objects.nonNull(comUniCodes), MvOnshoreBondCashDetailQuarterDO::getComUniCode, x -> x.in(comUniCodes))
                        .groupBy(MvOnshoreBondCashDetailQuarterDO::getDataYear, MvOnshoreBondCashDetailQuarterDO::getDataQuarter);
        return mvOnshoreBondCashDetailQuarterGroupMapper.selectByGroupedQuery(groupedQuery);
    }


}
