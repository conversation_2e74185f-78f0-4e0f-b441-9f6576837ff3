package com.innodealing.onshore.bondservice.model.dto.request;


import io.swagger.annotations.ApiModelProperty;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondCashDistributionPieChartRequestDTO {

    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("筛选类型：1.债券类型 2.剩余期限")
    private Integer filterType;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getFilterType() {
        return filterType;
    }

    public void setFilterType(Integer filterType) {
        this.filterType = filterType;
    }
}
