package com.innodealing.onshore.bondservice.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import tk.mybatis.spring.annotation.MapperScan;
import javax.sql.DataSource;
import static com.innodealing.onshore.config.datasource.postgresql.PostgresqlDefaultDataSourceConfig.DATA_SOURCE_NAME;

/**
 * pg bond 配置类
 * <AUTHOR>
 * @date 2025/06/16
 */
@Configuration
@MapperScan(basePackages = {"com.innodealing.onshore.bondservice.mapper.pgbond"},
        sqlSessionFactoryRef = PgBondDataSourceConfig.SESSION_FACTORY_NAME)
public class PgBondDataSourceConfig extends BaseSourceConfig {

    public static final String TRANSACTION_NAME = "pgbondTransactionManager";
    private static final String DATA_SOURCE_PREFIX = "pgbond.datasource";
    public static final String SESSION_FACTORY_NAME = "pgbondSqlSessionFactory";
    protected static final String[] ALIAS_PACKAGES = {"com.innodealing.onshore.bondservice.model.entity.pgbond"};
    public static final String DATABASE_NAME = "bond";


    /**
     * 创建数据源
     *
     * @return 返回数据源
     */
    @Bean(name = DATA_SOURCE_NAME, initMethod = "init", destroyMethod = "close")
    @ConfigurationProperties(prefix = DATA_SOURCE_PREFIX)
    public DruidDataSource dataSource() {
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        build.setName(DATABASE_NAME);
        return build;
    }

    /**
     * 配置事务
     *
     * @return 事务
     */
    @Bean(name = TRANSACTION_NAME)
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }

    /**
     * 创建SqlSessionFactory对象
     *
     * @param dataSource 数据源
     * @return SqlSessionFactory对象
     * @throws Exception 异常
     */
    @Bean(name = SESSION_FACTORY_NAME)
    public SqlSessionFactory sqlSessionFactory(@Qualifier(DATA_SOURCE_NAME) DataSource dataSource) throws Exception {
        return super.getSessionFactory(dataSource, ALIAS_PACKAGES);
    }

    /**
     * 创建JdbcTemplate对象
     *
     * @param dataSource 数据源
     * @return JdbcTemplate对象
     */
    @Bean
    public JdbcTemplate jdbcTemplate(@Qualifier(DATA_SOURCE_NAME) DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

}
