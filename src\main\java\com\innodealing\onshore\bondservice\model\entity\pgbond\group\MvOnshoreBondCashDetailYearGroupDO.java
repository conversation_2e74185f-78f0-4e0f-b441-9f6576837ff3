package com.innodealing.onshore.bondservice.model.entity.pgbond.group;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * 国内债券现金流按年统计物化视图
 *
 * <AUTHOR>
 */
public class MvOnshoreBondCashDetailYearGroupDO {
    /**
     * 数据年份
     */
    @Column
    private Integer dataYear;
    /**
     * 偿付利息(万)
     */
    @Column(name="sum(total_pay_interest_cash)")
    private BigDecimal totalPayInterestCash;
    /**
     * 偿付本金(万)
     */
    @Column(name="sum(total_pay_principal_cash)")
    private BigDecimal totalPayPrincipalCash;
    /**
     * 行权偿付利息(万)
     */
    @Column(name="sum(total_exercise_pay_interest_cash)")
    private BigDecimal totalExercisePayInterestCash;
    /**
     * 行权偿付本金(万)
     */
    @Column(name="sum(total_exercise_pay_principal_cash)")
    private BigDecimal totalExercisePayPrincipalCash;

    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }

    public BigDecimal getTotalPayInterestCash() {
        return totalPayInterestCash;
    }

    public void setTotalPayInterestCash(BigDecimal totalPayInterestCash) {
        this.totalPayInterestCash = totalPayInterestCash;
    }

    public BigDecimal getTotalPayPrincipalCash() {
        return totalPayPrincipalCash;
    }

    public void setTotalPayPrincipalCash(BigDecimal totalPayPrincipalCash) {
        this.totalPayPrincipalCash = totalPayPrincipalCash;
    }

    public BigDecimal getTotalExercisePayInterestCash() {
        return totalExercisePayInterestCash;
    }

    public void setTotalExercisePayInterestCash(BigDecimal totalExercisePayInterestCash) {
        this.totalExercisePayInterestCash = totalExercisePayInterestCash;
    }

    public BigDecimal getTotalExercisePayPrincipalCash() {
        return totalExercisePayPrincipalCash;
    }

    public void setTotalExercisePayPrincipalCash(BigDecimal totalExercisePayPrincipalCash) {
        this.totalExercisePayPrincipalCash = totalExercisePayPrincipalCash;
    }
}

