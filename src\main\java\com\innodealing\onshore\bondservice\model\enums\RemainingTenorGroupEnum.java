package com.innodealing.onshore.bondservice.model.enums;

import com.google.common.collect.Range;

import java.util.Arrays;

/**
 * 剩余期限分组枚举
 * 使用Guava Range进行优雅的期限区间匹配
 *
 * <AUTHOR>
 * @date 2025/01/13
 */
public enum RemainingTenorGroupEnum {
    
    WITHIN_ONE_YEAR(1, "1年以内", Range.closedOpen(0, 365)),
    ONE_TO_THREE_YEARS(2, "1~3年", Range.closedOpen(365, 1095)),
    THREE_TO_FIVE_YEARS(3, "3~5年", Range.closedOpen(1095, 1825)),
    OVER_FIVE_YEARS(4, "5年以上", Range.atLeast(1825));

    private final Integer code;
    private final String displayName;
    private final Range<Integer> dayRange;

    RemainingTenorGroupEnum(Integer code, String displayName, Range<Integer> dayRange) {
        this.code = code;
        this.displayName = displayName;
        this.dayRange = dayRange;
    }

    public Integer getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public Range<Integer> getDayRange() {
        return dayRange;
    }

    /**
     * 根据剩余天数获取对应的期限分组
     * 
     * @param remainingDays 剩余天数
     * @return 对应的分组枚举，如果没有匹配则返回null
     */
    public static RemainingTenorGroupEnum getGroupByRemainingDays(Integer remainingDays) {
        if (remainingDays == null || remainingDays < 0) {
            return null;
        }
        
        return Arrays.stream(values())
                .filter(group -> group.getDayRange().contains(remainingDays))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据code获取枚举
     * 
     * @param code 分组编码
     * @return 对应的枚举，如果没有找到则返回null
     */
    public static RemainingTenorGroupEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(group -> group.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查给定天数是否在指定范围内
     * 
     * @param remainingDays 剩余天数
     * @return 是否匹配当前分组
     */
    public boolean matches(Integer remainingDays) {
        return remainingDays != null && dayRange.contains(remainingDays);
    }
}