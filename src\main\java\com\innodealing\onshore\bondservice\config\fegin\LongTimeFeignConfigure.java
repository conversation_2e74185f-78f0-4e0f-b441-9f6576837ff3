package com.innodealing.onshore.bondservice.config.fegin;

//超时时间设置,开启重试机制，默认为5次（包含首次请求）

import feign.Request;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * Feign 配置, 尝试按
 *
 * <AUTHOR>
 */
@Configuration
public class LongTimeFeignConfigure {

    private static final int CONNECT_TIME_OUT_MILLIS = 600_000;
    private static final int READ_TIME_OUT_MILLIS = 600_000;

    /**
     * 参数
     *
     * @return 请求参数
     */
    @Bean
    public Request.Options options() {
        return new Request.Options(CONNECT_TIME_OUT_MILLIS, TimeUnit.MILLISECONDS, READ_TIME_OUT_MILLIS, TimeUnit.MILLISECONDS, true);
    }
}
