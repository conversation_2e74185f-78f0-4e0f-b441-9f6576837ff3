package com.innodealing.onshore.bondservice.service.processor.calculator.impl;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondservice.config.constant.NumberConstant;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.service.processor.calculator.AbstractBondCashDetailCalculator;
import com.innodealing.onshore.bondservice.utils.BondCashCalculationUtils;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Date;
import java.util.*;

import static com.innodealing.onshore.bondservice.utils.BondCashCalculationUtils.calculateInterestCash;

/**
 * 偿付利息 计算器
 * 负责计算偿付利息字段
 *
 * <AUTHOR>
 */
@Component
public class PayInterestCashCalculator extends AbstractBondCashDetailCalculator {

    @Override
    public String getName() {
        return PayInterestCashCalculator.class.getName();
    }

    @Override
    public int getPriority() {
        return NumberConstant.INT_THREE;
    }

    @Override
    protected void doCalculate(BondProcessContext context, Long bondUniCode) {
        // 使用工具类进行数据验证
        if (!BondCashCalculationUtils.validateContextData(context, bondUniCode, PayInterestCashCalculator.class.getName())) {
            return;
        }

        // 使用工具类分组债券金额和现金流数据
        Map<String, List<BondAmountDTO>> sameYearMonthAmountDataList = BondCashCalculationUtils.groupBondAmountsByKey(
                context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList()),
                this::getBondAmountKey
        );

        Map<String, List<BondCashFlowViewDTO>> sameYearMonthBondCashFlowDataList = BondCashCalculationUtils.groupBondCashFlowsByKey(
                context.getBondCashFlowMap().getOrDefault(bondUniCode, Collections.emptyList()),
                this::getBondCashFlowKey
        );


        // 处理每个月的数据
        for (List<BondCashFlowViewDTO> cashList : sameYearMonthBondCashFlowDataList.values()) {
            Date date = cashList.stream().findFirst().map(BondCashFlowViewDTO::getInterestEndDate).orElse(null);
            if (cashList.isEmpty() || Objects.isNull(date)) {
                continue;
            }
            Date exerciseDate = Date.valueOf(date.toLocalDate().plusDays(1L));

            // 查找最新剩余规模
            Optional<BondAmountDTO> filterBondAmountOpt = sameYearMonthAmountDataList.values().stream()
                    .flatMap(List::stream)
                    .filter(v -> Objects.nonNull(v.getChangeDate()) && v.getChangeDate().before(date))
                    .max(Comparator.comparing(BondAmountDTO::getChangeDate)
                            .thenComparing(BondAmountDTO::getRemainAmount, Comparator.reverseOrder()));

            if (!filterBondAmountOpt.isPresent() || Objects.isNull(filterBondAmountOpt.get().getRemainAmount())) {
                logger.warn("processNonRightBondInterest 最新剩余规模不存在,跳过：com_uni_code:{},bond_uni_code:{}",
                        context.getComUniCode(), bondUniCode);
                continue;
            }

            OnshoreBondCashDetailBO cashDetail = context.getOrCreateCashDetail(bondUniCode, exerciseDate);
            BigDecimal latestRemainAmount = filterBondAmountOpt.get().getRemainAmount();

            // 计算利息现金流
            calculateInterestCash(cashList, latestRemainAmount).ifPresent(amount -> {
                cashDetail.setPayInterestCash(amount);
                cashDetail.setTheoryInterestDate(exerciseDate);
            });
        }
    }
}
