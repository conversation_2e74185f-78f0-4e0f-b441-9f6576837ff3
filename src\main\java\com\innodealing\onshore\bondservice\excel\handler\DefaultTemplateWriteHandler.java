package com.innodealing.onshore.bondservice.excel.handler;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.property.ExcelWriteHeadProperty;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 默认logo导出模板
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
public class DefaultTemplateWriteHandler implements CellWriteHandler {

    private static final String FONT_NAME = "微软雅黑";
    private static final double CONVERSION = 1.333;
    private static final int UNIT_OFFSET = 9525;
    private static final String LOGO_URL = "https://dmdata-shanghai.oss-cn-shanghai.aliyuncs.com/public/bond-web/lQLPJxa0yq-kWPhdzMiwQFg9-II1ZB0DKUaHDgAQAA_200_93.png";
    private static final int CELL_WIDTH = 15;
    private static final short HEAD_CELL_HEIGHT = 35;
    private static final short CELL_HEIGHT = 18;
    private static final short FONT_SIZE = 12;
    private static final int TWO = 2;
    private final Logger logger = LoggerFactory.getLogger(DefaultTemplateWriteHandler.class);
    private final String title;
    private final String excelName;

    /**
     * default模板编写处理程序
     *
     * @param title            标题
     * @param excelName        excel名称
     */
    public DefaultTemplateWriteHandler(String title, String excelName) {
        this.title = title;
        this.excelName = excelName;
    }

    /**
     * 在创建单元格之前调用
     *
     * @param writeSheetHolder writeSheetHolder
     * @param writeTableHolder Nullable.在不使用表写入的情况下为空
     * @param head             Nullable.在有填充数据且没有头部的情况下为空
     * @param relativeRowIndex Nullable.在填充数据的情况下为空
     * @param isHead           It will always be false when fill data.
     * @param row              行
     * @param columnIndex      列索引
     */
    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
                                 Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        if (isHead && relativeRowIndex == 0) {
            ExcelWriteHeadProperty excelWriteHeadProperty = writeSheetHolder.getExcelWriteHeadProperty();
            List<String> headNameList = head.getHeadNameList();
            excelWriteHeadProperty.setHeadRowNumber(headNameList.size() + 1);
            List<String> headNames = new ArrayList<>(headNameList.size() + 1);
            if (columnIndex == 0) {
                headNames.add("");
            } else {
                headNames.add(this.title);
            }
            headNames.addAll(headNameList);
            head.setHeadNameList(headNames);
        }
        Sheet sheet = writeSheetHolder.getSheet();
        sheet.setDefaultColumnWidth(CELL_WIDTH);
        sheet.setDefaultRowHeightInPoints(CELL_HEIGHT);
        if (isHead) {
            row.setHeightInPoints(HEAD_CELL_HEIGHT);
        } else {
            row.setHeightInPoints(CELL_HEIGHT);
        }
    }

    /**
     * 创建单元格后调用
     *
     * @param writeTableHolder Nullable.在不使用表写入的情况下为空
     * @param head             Nullable.在有填充数据且没有头部的情况下为空
     * @param relativeRowIndex Nullable.在填充数据的情况下为空
     * @param isHead           填充数据时它总是错误的
     * @param writeSheetHolder writeSheetHolder
     * @param cell             cell
     */
    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        if (cell.getRowIndex() == 0 && cell.getColumnIndex() == 0) {
            cellImage(workbook, cell.getSheet(), LOGO_URL, 0);
            ExcelWriteHeadProperty excelWriteHeadProperty = writeSheetHolder.getExcelWriteHeadProperty();
            // 列合并  大于2列才合并第2-最后列，不然会报错。
            if (excelWriteHeadProperty.getHeadMap().size() > TWO) {
                CellRangeAddress callRangeAddress =
                        new CellRangeAddress(0, 0, 1, excelWriteHeadProperty.getHeadMap().size() - 1);
                Sheet sheet = writeSheetHolder.getSheet();
                sheet.addMergedRegion(callRangeAddress);
            }
        }
    }

    /**
     * 写入logo
     *
     * @param imgUrl   图片的资源路径
     * @param workbook 操作book
     * @param sheet    sheet
     * @param rowIndex 行号
     */
    public void cellImage(Workbook workbook, Sheet sheet, String imgUrl, int rowIndex) {
        // 图片
        BufferedImage bufferImg;
        ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
        try {
            bufferImg = ImageIO.read(new URL(imgUrl));
            // 将图片写入流中(png变成jpg会导致图片颜色不对)
            ImageIO.write(bufferImg, "png", byteArrayOut);
            // 利用HSSFPatriarch将图片写入EXCEL
            Drawing<?> patriarch = sheet.createDrawingPatriarch();
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 0, 0, (short) 0, 0);
            // 设置图片的高度为接近行高的整数，图片宽度为高度的2倍
            int rowHeightInPoints = (int) Math.round((sheet.getRow(rowIndex).getHeightInPoints() * CONVERSION));
            anchor.setDx2(rowHeightInPoints * TWO * UNIT_OFFSET);
            anchor.setDy2(rowHeightInPoints * UNIT_OFFSET);
            anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
            patriarch.createPicture(anchor, workbook.addPicture(byteArrayOut.toByteArray(), HSSFWorkbook.PICTURE_TYPE_JPEG));
            byteArrayOut.close();
        } catch (IOException e) {
            logger.error("excel写入logo出错：msg :{}", e.getMessage(), e);
        }
    }

    /**
     * 在单元格上的所有操作完成后调用
     *
     * @param context context
     */
    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        WriteCellData<?> firstCellData = context.getFirstCellData();
        WriteCellStyle writeCellStyle = firstCellData.getOrCreateStyle();
        WriteFont writeFont = ObjectUtils.defaultIfNull(writeCellStyle.getWriteFont(), new WriteFont());
        writeFont.setFontName(FONT_NAME);
        if (context.getHead()) {
            writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            if (context.getRowIndex() == 0) {
                writeFont.setColor(IndexedColors.BROWN.index);
                writeCellStyle.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.index);
            } else {
                writeFont.setColor(IndexedColors.BLACK.index);
                writeCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
                setBorderStyle(writeCellStyle);
            }
            writeCellStyle.setWrapped(true);
        } else {
            // 设置数据内容格式
            if (Objects.equals(firstCellData.getType(), CellDataTypeEnum.NUMBER)) {
                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.RIGHT);
            } else {
                writeCellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
            }
            setBorderStyle(writeCellStyle);
        }
        writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        writeFont.setFontHeightInPoints(FONT_SIZE);
        writeCellStyle.setWriteFont(writeFont);
    }

    private void setBorderStyle(WriteCellStyle writeCellStyle) {
        // 设置边框样式
        writeCellStyle.setBorderRight(BorderStyle.THIN);
        writeCellStyle.setBorderLeft(BorderStyle.THIN);
        writeCellStyle.setBorderTop(BorderStyle.THIN);
        writeCellStyle.setBorderBottom(BorderStyle.THIN);
    }


    /**
     * 当前单元格向上合并
     *
     * @param writeSheetHolder 表格处理句柄
     * @param cell             当前单元格
     * @param curRowIndex      当前行
     * @param curColIndex      当前列
     */
    private void mergeData(WriteSheetHolder writeSheetHolder, Cell cell, int curRowIndex, int curColIndex) {
        // 获取当前单元格数值
        Object curData = cell.getCellType() == CellType.STRING ? cell.getStringCellValue() : cell.getNumericCellValue();
        // 获取当前单元格正上方的单元格对象
        Cell preCell = cell.getSheet().getRow(curRowIndex - 1).getCell(curColIndex);
        // 获取当前单元格正上方的单元格的数值
        Object preData = preCell.getCellType() == CellType.STRING ? preCell.getStringCellValue() : preCell.getNumericCellValue();
        // 合并唯一标识，其他列的合并要与第一列对齐，第一列没合并，其他列就算值一样也不能合并
        String curRowCellOneData = cell.getRow().getCell(0).getStringCellValue();
        String preRowCellOneData = cell.getSheet().getRow(curRowIndex - 1).getCell(0).getStringCellValue();
        if (Objects.equals(curData, preData) && Objects.equals(curRowCellOneData, preRowCellOneData)) {
            Sheet sheet = writeSheetHolder.getSheet();
            List<CellRangeAddress> mergeRegions = sheet.getMergedRegions();
            boolean isMerged = false;
            for (int i = 0; i < mergeRegions.size() && !isMerged; i++) {
                CellRangeAddress cellRangeAddr = mergeRegions.get(i);
                // 若上一个单元格已经被合并，则先移出原有的合并单元，再重新添加合并单元
                if (cellRangeAddr.isInRange(curRowIndex - 1, curColIndex)) {
                    sheet.removeMergedRegion(i);
                    cellRangeAddr.setLastRow(curRowIndex);
                    sheet.addMergedRegion(cellRangeAddr);
                    isMerged = true;
                }
            }
            // 若上一个单元格未被合并，则新增合并单元
            if (!isMerged) {
                CellRangeAddress cellRangeAddress = new CellRangeAddress(curRowIndex - 1, curRowIndex, curColIndex, curColIndex);
                sheet.addMergedRegion(cellRangeAddress);
            }
        }
    }
}