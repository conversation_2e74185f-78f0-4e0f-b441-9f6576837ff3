package com.innodealing.onshore.bondservice.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.util.Objects;

/**
 * 没有权限序列化显示
 *
 * <AUTHOR>
 * @date 2025/06/26
 */
public class DisplayStringNoPermissionsJsonSerializer extends JsonSerializer<String> {
    private static final String NOT_AUTH_PLACEHOLDER = "**";
    private static final String EMPTY_DEFAULT_PLACEHOLDER = "--";
    public static final String NOT_AUTH_FLAG = "-9999999";


    @Override
    public void serialize(String s, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        String displayStr = getDisplayString(s);
        jsonGenerator.writeString(displayStr);
    }

    private String getDisplayString(String s) {
        if (Objects.isNull(s)) {
            return EMPTY_DEFAULT_PLACEHOLDER;
        }
        if (NOT_AUTH_FLAG.equals(s)) {
            return NOT_AUTH_PLACEHOLDER;
        }
        return s;
    }
}