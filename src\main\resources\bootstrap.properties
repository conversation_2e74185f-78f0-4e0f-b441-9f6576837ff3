spring.application.name=onshore-bond-service
# prd nacos property
spring.cloud.nacos.config.server-addr=${NACOS_ADDR:mse-4d0af020-nacos-ans.mse.aliyuncs.com:8848}
spring.cloud.nacos.config.namespace=${NACOS_NAMESPACE:dev}
spring.cloud.nacos.config.access-key=${NACOS_GENERAL_ACCESS_KEY:LTAI5tJ72ozLyf6eQnsupgV5}
spring.cloud.nacos.config.secret-key=${NACOS_GENERAL_SECRET_KEY:******************************}
spring.cloud.nacos.config.refresh-enabled=true

# 共享配置
spring.cloud.nacos.config.extension-configs[0].data-id=onshore-bond-service.properties
spring.cloud.nacos.config.extension-configs[0].group=onshore-bond-service
spring.cloud.nacos.config.extension-configs[0].refresh=true
spring.cloud.nacos.config.extension-configs[1].data-id=pg_onshore_conn.properties
spring.cloud.nacos.config.extension-configs[1].group=public
spring.cloud.nacos.config.extension-configs[1].refresh=true
spring.cloud.nacos.config.extension-configs[2].data-id=polar_general_conn.properties
spring.cloud.nacos.config.extension-configs[2].group=public
spring.cloud.nacos.config.extension-configs[2].refresh=true
spring.cloud.nacos.config.extension-configs[3].data-id=redis_onshore_conn.properties
spring.cloud.nacos.config.extension-configs[3].group=public
spring.cloud.nacos.config.extension-configs[3].refresh=true
spring.cloud.nacos.config.extension-configs[4].data-id=redis_innodealing_conn.properties
spring.cloud.nacos.config.extension-configs[4].group=public
spring.cloud.nacos.config.extension-configs[4].refresh=true
spring.cloud.nacos.config.extension-configs[5].data-id=kafka_datasync_conn.properties
spring.cloud.nacos.config.extension-configs[5].group=public
spring.cloud.nacos.config.extension-configs[5].refresh=true
pagehelper.autoRuntimeDialect=true
logging.level.com.innodealing.onshore.bondservice.mapper=debug

