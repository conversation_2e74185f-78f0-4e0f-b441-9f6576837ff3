package com.innodealing.onshore.bondservice.model.dto.response;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayNumber4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 流通中债券-规模分布饼图响应
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondCashDistributionPieChartResponseDTO {
    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("筛选类型：1.债券类型 2.剩余期限")
    private Integer filterType;
    @ApiModelProperty("债券类型: 1 国债 2地方政府一般债...\n" +
            "剩余期限类型:  1. 1年以内 2.1~3年（包含1年） 3.3~5年（包含3年） 4.5年以上（包含5年） ")
    private Integer aggregationType;
    @ApiModelProperty("聚合类型描述")
    private String aggregationTypeDesc;
    @ApiModelProperty("债券数据条数")
    private Integer bondCount;
    @ApiModelProperty("剩余规模金额")
    @JsonSerialize(using = DisplayNumber4ScaleJsonSerializer.class, nullsUsing = DisplayNumber4ScaleJsonSerializer.class)
    private BigDecimal remainAmount;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getFilterType() {
        return filterType;
    }

    public void setFilterType(Integer filterType) {
        this.filterType = filterType;
    }

    public Integer getAggregationType() {
        return aggregationType;
    }

    public void setAggregationType(Integer aggregationType) {
        this.aggregationType = aggregationType;
    }

    public BigDecimal getRemainAmount() {
        return remainAmount;
    }

    public void setRemainAmount(BigDecimal remainAmountRatio) {
        this.remainAmount = remainAmountRatio;
    }

    public String getAggregationTypeDesc() {
        return aggregationTypeDesc;
    }

    public void setAggregationTypeDesc(String aggregationTypeDesc) {
        this.aggregationTypeDesc = aggregationTypeDesc;
    }

    public Integer getBondCount() {
        return bondCount;
    }

    public void setBondCount(Integer bondCount) {
        this.bondCount = bondCount;
    }
}
