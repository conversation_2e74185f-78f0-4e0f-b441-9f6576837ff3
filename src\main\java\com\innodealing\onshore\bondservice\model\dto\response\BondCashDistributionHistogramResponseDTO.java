package com.innodealing.onshore.bondservice.model.dto.response;


import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 流通中债券-规模分布柱状图
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondCashDistributionHistogramResponseDTO {
    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("相同实控人列表")
    private List<BondCashPayInfoDTO> relationEnterpriseList;
    @ApiModelProperty("本主体列表")
    private List<BondCashPayInfoDTO> currentEnterpriseList;
    @ApiModelProperty("ABS列表")
    private List<BondCashPayInfoDTO> absEnterpriseList;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public List<BondCashPayInfoDTO> getRelationEnterpriseList() {
        return relationEnterpriseList;
    }

    public void setRelationEnterpriseList(List<BondCashPayInfoDTO> relationEnterpriseList) {
        this.relationEnterpriseList = relationEnterpriseList;
    }

    public List<BondCashPayInfoDTO> getCurrentEnterpriseList() {
        return currentEnterpriseList;
    }

    public void setCurrentEnterpriseList(List<BondCashPayInfoDTO> currentEnterpriseList) {
        this.currentEnterpriseList = currentEnterpriseList;
    }

    public List<BondCashPayInfoDTO> getAbsEnterpriseList() {
        return absEnterpriseList;
    }

    public void setAbsEnterpriseList(List<BondCashPayInfoDTO> absEnterpriseList) {
        this.absEnterpriseList = absEnterpriseList;
    }
}
