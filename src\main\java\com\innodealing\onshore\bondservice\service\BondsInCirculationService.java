package com.innodealing.onshore.bondservice.service;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondservice.model.dto.request.*;
import com.innodealing.onshore.bondservice.model.dto.response.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 流通中债券重构Service
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public interface BondsInCirculationService {

    /**
     * 获取流通中债券的流现分布直方图
     *
     * @param requestDTOList 请求参数
     * @return 响应参数
     */
    BondCashDistributionHistogramResponseDTO bondCashDistributionHistogram(BondCashDistributionHistogramRequestDTO requestDTOList);

    /**
     * 获取流通中债券的流现分布详情分页
     *
     * @param requestDTO 详情分页请求参数
     * @return 响应参数
     */
    NormPagingResult<BondCashDistributionDetailPageResponseDTO> getBondCashDistributionDetailPaging(BondCashDistributionDetailPageRequestDTO requestDTO);

    /**
     * 获取流通中债券的流现分布详情导出
     *
     * @param requestDTO 详情分页请求参数
     * @param response   响应参数
     */
    void exportBondCashDistributionDetail(BondCashDistributionDetailPageRequestDTO requestDTO, HttpServletResponse response) throws IOException;

    /**
     * 获取流通中债券的流现分布饼图
     *
     * @param requestDTO 饼图请求参数
     * @return 响应参数
     */
    List<BondCashDistributionPieChartResponseDTO> bondCashDistributionPieCharts(BondCashDistributionPieChartRequestDTO requestDTO);

    /**
     * 获取流通中债券的明细分页
     *
     * @param requestDTO 明分页请求参数
     * @param userId 用户id
     * @return 响应参数
     */
    NormPagingResult<BondDetailInfoPageResponseDTO> getBondDetailInfoPaging(BondDetailInfoPageRequestDTO requestDTO, Long userId);


    /**
     * 获取流通中债券的明细导出
     *
     * @param requestDTO 明分页请求参数
     * @param userId 用户id
     * @param response 响应参数
     */
    void exportBondDetailInfo(BondDetailInfoPageExportRequestDTO requestDTO, Long userId, HttpServletResponse response) throws IOException;

    /**
     * 获取主体流通中主体债券信息
     *
     * @param comUniCode 主体id
     * @return 响应参数
     */
    ComDistributionDetailResponseDTO getComDistributionDetailResponseDTO(Long comUniCode);

    /**
     * 获取主体流转中债券余额统计信息
     *
     * @param requestDTO 统计信息请求参数
     * @return 响应参数
     */
    List<BondBalanceStatisticsResponseDTO> listBondBalanceStatistics(BondBalanceStatisticsRequestDTO requestDTO);
}
