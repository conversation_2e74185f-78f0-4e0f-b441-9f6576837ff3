package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 发行人基础信息服务
 *
 * <AUTHOR>
 * @date ：2024/03/18 11:10
 */
@FeignClient(name = "comBasicInfoService", url = "${onshore.com.url}")
public interface ComBasicInfoService {

    /**
     * 批量获取发行人信息-- 根据主体唯一编码
     *
     * @param comUniCodes 主体唯一编码
     * @return java.util.List<com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO> 发行人精简信息DTO集合
     * <AUTHOR>
     */
    @PostMapping("internal/com/info/short/getByUnicode")
    List<ComShortInfoDTO> listComShortInfoByUniCodes(@RequestBody Long... comUniCodes);


}
