package com.innodealing.onshore.bondservice.excel.conveter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.excel.util.WorkBookUtil;

import java.util.Date;

/**
 * SQL.Date 转换 String 器
 * 日期格式：yyyy-MM-dd
 *
 * <AUTHOR>
 * @date 2024/02/26
 */
public class DateDateFormat10Converter implements Converter<Date> {
    @Override
    public Class<Date> supportJavaTypeKey() {
        return Date.class;
    }

    @Override
    public WriteCellData<?> convertToExcelData(Date value, ExcelContentProperty contentProperty,
                                               GlobalConfiguration globalConfiguration) throws Exception {
        // sql.Date 转为 util.Date
        if (value instanceof java.sql.Date) {
            value = new Date(((java.sql.Date) value).getTime());
        }
        WriteCellData<?> cellData = new WriteCellData<>(value);
        String format = null;
        if (contentProperty != null && contentProperty.getDateTimeFormatProperty() != null) {
            format = contentProperty.getDateTimeFormatProperty().getFormat();
        }
        WorkBookUtil.fillDataFormat(cellData, format, DateUtils.DATE_FORMAT_10);
        return cellData;
    }
}