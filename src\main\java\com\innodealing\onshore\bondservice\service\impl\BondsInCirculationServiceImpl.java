package com.innodealing.onshore.bondservice.service.impl;

import com.alibaba.excel.EasyExcel;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.innodealing.commons.excelutils.StringToSqlDateConvert;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.dto.bondabs.AbsBondComDetailInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondabs.AbsBondDetailInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondabs.request.AbsBondDetailBaseInfoRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondabs.request.AbsComBondDetailBaseInfoRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComBondDetailDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComRelatedPartyDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComShortInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.enums.BusinessNatureEnum;
import com.innodealing.onshore.bondmetadata.enums.PublicOffering;
import com.innodealing.onshore.bondmetadata.enums.SecondMarket;
import com.innodealing.onshore.bondmetadata.utils.EnumUtils;
import com.innodealing.onshore.bondservice.config.constant.RedisKeyConstant;
import com.innodealing.onshore.bondservice.dao.bond.OnshoreBondFilterDAO;
import com.innodealing.onshore.bondservice.dao.pgbond.PgOnshoreBondCashDetailDAO;
import com.innodealing.onshore.bondservice.excel.handler.CustomNullValueHandler;
import com.innodealing.onshore.bondservice.excel.handler.DefaultTemplateWriteHandler;
import com.innodealing.onshore.bondservice.excel.handler.DynamicColumnSortingTemplateWriteHandler;
import com.innodealing.onshore.bondservice.model.bo.BondBalanceStatisticsBO;
import com.innodealing.onshore.bondservice.model.dto.AbsBondShortInfoDTO;
import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import com.innodealing.onshore.bondservice.model.dto.RelationComShortInfoDTO;
import com.innodealing.onshore.bondservice.model.dto.request.*;
import com.innodealing.onshore.bondservice.model.dto.response.*;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView;
import com.innodealing.onshore.bondservice.model.entity.pgbond.PgOnshoreBondCashDetailDO;
import com.innodealing.onshore.bondservice.model.enums.AbsPartyTypeEnum;
import com.innodealing.onshore.bondservice.model.enums.BondTypeGroupEnum;
import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;
import com.innodealing.onshore.bondservice.model.enums.RemainingTenorGroupEnum;
import com.innodealing.onshore.bondservice.serializer.DisplayNumberFormat2ScaleJsonSerializer;
import com.innodealing.onshore.bondservice.serializer.DisplayStringNoPermissionsJsonSerializer;
import com.innodealing.onshore.bondservice.service.BondsInCirculationService;
import com.innodealing.onshore.bondservice.service.MvOnshoreBondCashDetailStrategy;
import com.innodealing.onshore.bondservice.service.MvOnshoreBondCashDetailStrategyFactory;
import com.innodealing.onshore.bondservice.service.internal.*;
import com.innodealing.onshore.bondservice.utils.UserColumnUtil;
import com.innodealing.onshore.service.CacheOptionalService;
import jodd.util.StringPool;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.*;
import static com.innodealing.onshore.bondservice.config.constant.RedisKeyConstant.REDIS_KEY_ABS_BOND_UNI_CODE_KEY;
import static com.innodealing.onshore.bondservice.config.constant.RedisKeyConstant.REDIS_KEY_RELATED_PARTY_COM_UNI_CODE_KEY;

/**
 * 流通中债券重构Service
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@Service
public class BondsInCirculationServiceImpl implements BondsInCirculationService {
    private static final Logger log = LoggerFactory.getLogger(BondsInCirculationServiceImpl.class);
    @Resource
    private OnshoreBondAbsHttpService onshoreBondAbsHttpService;
    @Resource
    private DwsPartyInfoServiceHttpService dwsPartyInfoServiceHttpService;
    @Resource
    private DwsBondInfoServiceHttpService dwsBondInfoServiceHttpService;
    @Resource
    private BondInfoService bondInfoService;
    @Resource
    private ComBasicInfoService comBasicInfoService;
    @Resource
    private PgOnshoreBondCashDetailDAO pgOnshoreBondCashDetailDAO;
    @Resource
    private OnshoreBondFilterDAO onshoreBondFilterDAO;
    @Resource
    private MvOnshoreBondCashDetailStrategyFactory mvOnshoreBondCashDetailStrategyFactory;
    @Resource
    private UserService userService;
    @Resource
    private CacheOptionalService cacheOptionalService;

    // 默认查询abs主体类型列表
    private static final List<Integer> ABS_PARTY_TYPES = Collections.unmodifiableList(Arrays.asList(4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14));
    private static final List<Integer> SEARCH_ABS_PARTY_TYPES = Collections.unmodifiableList(Arrays.asList(2, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14));
    private static final String TABLE_TITLE = "数据来源：DM             ";
    private static final String BOND_DETAIL_EXCEL_NAME = "流通中的债券-在岸”";
    private static final String BOND_CASH_EXCEL_NAME = "偿还流水";


    @Override
    public BondCashDistributionHistogramResponseDTO bondCashDistributionHistogram(BondCashDistributionHistogramRequestDTO requestDTO) {
        BondCashDistributionHistogramResponseDTO responseDTO = new BondCashDistributionHistogramResponseDTO();
        if (Objects.isNull(requestDTO.getComUniCode())) {
            return responseDTO;
        }
        responseDTO.setComUniCode(requestDTO.getComUniCode());
        MvOnshoreBondCashDetailStrategy strategy = mvOnshoreBondCashDetailStrategyFactory.getStrategy(DateTypeEnum.fromCode(requestDTO.getDateType()));
        List<BondCashPayInfoDTO> relationEnterpriseList = Collections.emptyList();
        List<BondCashPayInfoDTO> currentEnterpriseList;
        List<BondCashPayInfoDTO> absEnterpriseList = Collections.emptyList();
        // 本主体数据
        currentEnterpriseList = Objects.equals(requestDTO.getExerciseStatus(), 1)
                ? strategy.queryMvOnshoreBondCashDetailDataExercise(Sets.newHashSet(requestDTO.getComUniCode()))
                : strategy.queryMvOnshoreBondCashDetailDataNoExercise(Sets.newHashSet(requestDTO.getComUniCode()));
        responseDTO.setCurrentEnterpriseList(currentEnterpriseList);
        // 相同实控人数据
        if (Objects.equals(requestDTO.getCtrlTypeStatus(), 1)) {
            List<Long> relatedPartyComUniCodes = this.listCacheRelatedPartyComUniCodes(requestDTO.getComUniCode());
            if (CollectionUtils.isNotEmpty(relatedPartyComUniCodes)) {
                // 根据行权状态选择合适的查询策略
                relationEnterpriseList = Objects.equals(requestDTO.getExerciseStatus(), 1)
                        ? strategy.queryMvOnshoreBondCashDetailDataExercise(Sets.newHashSet(relatedPartyComUniCodes))
                        : strategy.queryMvOnshoreBondCashDetailDataNoExercise(Sets.newHashSet(relatedPartyComUniCodes));
            }
        }
        responseDTO.setRelationEnterpriseList(relationEnterpriseList);
        // ABS企业数据
        if (Objects.equals(requestDTO.getAbsTypeStatus(), 1)) {
            List<Long> absBondUniCodes = this.listCacheAbsBondUniCodes(requestDTO.getComUniCode());
            if (CollectionUtils.isNotEmpty(absBondUniCodes)) {
                // 根据债券维度统计
                absEnterpriseList = pgOnshoreBondCashDetailDAO.listBondCashPayInfoByTime(
                        absBondUniCodes, DateTypeEnum.fromCode(requestDTO.getDateType()), requestDTO.getExerciseStatus());
            }
        }
        responseDTO.setAbsEnterpriseList(absEnterpriseList);
        return responseDTO;
    }

    @Override
    public NormPagingResult<BondCashDistributionDetailPageResponseDTO> getBondCashDistributionDetailPaging(BondCashDistributionDetailPageRequestDTO requestDTO) {
        BondCashDistributionDetailPageRequestDTO cpReq = BeanCopyUtils.copyProperties(requestDTO, BondCashDistributionDetailPageRequestDTO.class);
        Set<Long> comUniCodes = Sets.newHashSet(requestDTO.getComUniCode());
        Set<Long> bondUniCodes = Sets.newHashSet(CollectionUtils.isEmpty(requestDTO.getBondUniCodes()) ? Collections.emptyList() : requestDTO.getBondUniCodes());
        if (Objects.equals(requestDTO.getAbsTypeStatus(), 1)) {
            bondUniCodes.addAll(this.listCacheAbsBondUniCodes(requestDTO.getComUniCode()));
        }
        if (Objects.equals(requestDTO.getCtrlTypeStatus(), 1)) {
            comUniCodes.addAll(dwsPartyInfoServiceHttpService.listRelatedPartyComUniCodes(requestDTO.getComUniCode()));
        }
        cpReq.setComUniCodes(Lists.newArrayList(comUniCodes));
        cpReq.setBondUniCodes(Lists.newArrayList(bondUniCodes));
        NormPagingResult<PgOnshoreBondCashDetailDO> onshoreBondCashDetailPaging = pgOnshoreBondCashDetailDAO.getOnshoreBondCashDetailPaging(cpReq);
        List<PgOnshoreBondCashDetailDO> list = onshoreBondCashDetailPaging.getList();
        if (CollectionUtils.isEmpty(list)) {
            return onshoreBondCashDetailPaging.convert(item -> BeanCopyUtils.copyProperties(item, BondCashDistributionDetailPageResponseDTO.class));
        }
        List<Long> bondUniCodesResp = list.stream().map(PgOnshoreBondCashDetailDO::getBondUniCode).distinct().collect(Collectors.toList());
        Map<Long, OnshoreBondInfoDTO> bondBasicDTOMap = Lists.partition(bondUniCodesResp, FIVE_HUNDRED).stream()
                .map(partition -> bondInfoService.listBondInfos(partition)).flatMap(Collection::stream)
                .collect(Collectors.toMap(OnshoreBondInfoDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
        return onshoreBondCashDetailPaging.convert(
                item -> {
                    BondCashDistributionDetailPageResponseDTO resp = BeanCopyUtils.copyProperties(item, BondCashDistributionDetailPageResponseDTO.class);
                    Optional.ofNullable(bondBasicDTOMap.get(item.getBondUniCode())).ifPresent(v -> {
                        resp.setBondCode(v.getBondCode());
                        resp.setBondShortName(v.getBondShortName());
                    });
                    return resp;
                });
    }

    @Override
    public void exportBondCashDistributionDetail(BondCashDistributionDetailPageRequestDTO requestDTO, HttpServletResponse response) throws IOException {
        BondCashDistributionDetailPageRequestDTO cpReq = BeanCopyUtils.copyProperties(requestDTO, BondCashDistributionDetailPageRequestDTO.class);
        ComShortInfoDTO first = comBasicInfoService.listComShortInfoByUniCodes(requestDTO.getComUniCode()).stream().findFirst().orElseGet(ComShortInfoDTO::new);
        // 最大导出限制
        cpReq.setPageNum(BigDecimal.ZERO.intValue());
        cpReq.setPageSize(TEN_THOUSAND.intValue());
        NormPagingResult<BondCashDistributionDetailPageResponseDTO> bondCashDistributionDetailPaging = this.getBondCashDistributionDetailPaging(cpReq);
        List<BondCashDistributionDetailPageResponseDTO> list = bondCashDistributionDetailPaging.getList();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        this.exportOperationByUserId(response, BeanCopyUtils.copyList(list, BondCashDistributionDetaiExportResponseDTO.class),
                first.getComFullName() + BOND_CASH_EXCEL_NAME + DateExtensionUtils.format(new Date(System.currentTimeMillis()), DateExtensionUtils.FORMAT_DATE_NUM),
                BondCashDistributionDetaiExportResponseDTO.class, 0L, Strings.EMPTY, Strings.EMPTY, Strings.EMPTY);
    }

    @Override
    public List<BondCashDistributionPieChartResponseDTO> bondCashDistributionPieCharts(BondCashDistributionPieChartRequestDTO requestDTO) {
        // 根据主体获取其下所有债券
        Map<Long, OnshoreBondFilterDO> bondFilterDOMap = onshoreBondFilterDAO.listOnshoreBondFilterDOs(requestDTO.getComUniCode(), BigDecimal.ONE.intValue()).stream()
                .collect(Collectors.toMap(OnshoreBondFilterDO::getBondId, Function.identity(), (v1, v2) -> v2));
        if (bondFilterDOMap.isEmpty()) {
            return Collections.emptyList();
        }
        List<BondCashDistributionPieChartResponseDTO> responseList = Lists.newArrayList();
        // 根据筛选类型进行分组统计
        if (Objects.equals(requestDTO.getFilterType(), 1)) {
            // 按债券类型分组
            responseList = this.groupByBondType(bondFilterDOMap, requestDTO.getComUniCode());
        }
        if (Objects.equals(requestDTO.getFilterType(), 2)) {
            // 按剩余期限分组
            responseList = this.groupByRemainingTenor(bondFilterDOMap, requestDTO.getComUniCode());
        }
        return responseList;
    }

    @Override
    public NormPagingResult<BondDetailInfoPageResponseDTO> getBondDetailInfoPaging(BondDetailInfoPageRequestDTO requestDTO, Long userId) {
        if (Objects.isNull(requestDTO.getComUniCode()) && CollectionUtils.isEmpty(requestDTO.getRelationComUniCodes())
                && CollectionUtils.isEmpty(requestDTO.getAbsBondUniCodes())) {
            return new NormPagingResult<>();
        }
        BondDetailInfoPageRequestDTO copied = BeanCopyUtils.copyProperties(requestDTO, BondDetailInfoPageRequestDTO.class);
        if (CollectionUtils.isNotEmpty(requestDTO.getBondTypes())) {
            copied.setBondTypes(requestDTO.getBondTypes().stream().map(BondTypeGroupEnum::getByCode).filter(Objects::nonNull)
                    .map(BondTypeGroupEnum::getBondTypes).flatMap(Collection::stream).collect(Collectors.toList()));
        }
        NormPagingResult<OnshoreBondFilterDO> page = onshoreBondFilterDAO.getOnshoreBondFilterPage(copied);
        if (CollectionUtils.isEmpty(page.getList())) {
            return new NormPagingResult<>();
        }
        List<Long> bondUniCodes = page.getList().stream().map(OnshoreBondFilterDO::getBondUniCode).distinct().collect(Collectors.toList());
        Map<Long/*bondUniCode*/, List<AbsBondDetailInfoDTO>> absBondDetailInfoMap = this.collectAbsBondDetailInfoMap(bondUniCodes);
        boolean cbPermission = userService.hasCbPermission(userId);
        boolean csPermission = userService.hasCsPermission(userId);
        boolean cbBondImpliedRatingPermission = userService.hasCbBondImpliedRatingPermission(userId);

        return page.convert(item -> convert(item, absBondDetailInfoMap, cbPermission, csPermission, cbBondImpliedRatingPermission));
    }

    @Override
    public void exportBondDetailInfo(BondDetailInfoPageExportRequestDTO requestDTO, Long userId, HttpServletResponse response) throws IOException {
        if (Objects.isNull(requestDTO.getComUniCode()) && CollectionUtils.isEmpty(requestDTO.getRelationComUniCodes())
                && CollectionUtils.isEmpty(requestDTO.getAbsBondUniCodes())) {
            return;
        }
        BondDetailInfoPageExportRequestDTO cpReq = BeanCopyUtils.copyProperties(requestDTO, BondDetailInfoPageExportRequestDTO.class);
        ComShortInfoDTO comShortInfoDTO = comBasicInfoService.listComShortInfoByUniCodes(requestDTO.getComUniCode()).stream().findFirst().orElseGet(ComShortInfoDTO::new);
        cpReq.setPageNum(BigDecimal.ONE.intValue());
        cpReq.setPageSize(TEN_THOUSAND.intValue());
        NormPagingResult<BondDetailInfoPageResponseDTO> bondDetailInfoPaging = this.getBondDetailInfoPaging(cpReq, userId);
        List<BondDetailInfoExportResponseDTO> list = bondDetailInfoPaging.getList()
                .stream().map(this::convertExportDto).collect(Collectors.toList());
        this.exportOperationByUserId(
                response, list,
                comShortInfoDTO.getComFullName() + BOND_DETAIL_EXCEL_NAME +
                        DateExtensionUtils.format(new Date(System.currentTimeMillis()), DateExtensionUtils.FORMAT_DATE_NUM),
                BondDetailInfoExportResponseDTO.class, userId, cpReq.getTableKey(), cpReq.getDefaultColumns(), "       " + requestDTO.getFilterDescriptor());
    }


    @Override
    public ComDistributionDetailResponseDTO getComDistributionDetailResponseDTO(Long comUniCode) {
        if (Objects.isNull(comUniCode)) {
            return new ComDistributionDetailResponseDTO();
        }
        Optional<ComDistributionDetailResponseDTO> cacheOptional =
                cacheOptionalService.get(String.format(RedisKeyConstant.REDIS_KEY_COM_INFO_F9_KEY, comUniCode), ComDistributionDetailResponseDTO.class);
        if (cacheOptional.isPresent()) {
            return cacheOptional.get();
        }
        StopWatch stopWatch = new StopWatch();
        Set<Long> comUniCodes = new HashSet<>();
        comUniCodes.add(comUniCode);
        Optional<ComShortInfoDTO> first = comBasicInfoService.listComShortInfoByUniCodes(comUniCode).stream().findFirst();
        ComDistributionDetailResponseDTO comDistributionDetailResponseDTO = new ComDistributionDetailResponseDTO();
        comDistributionDetailResponseDTO.setComUniCode(comUniCode);
        if (!first.isPresent() || Objects.isNull(first.get().getBusinessNature())) {
            comDistributionDetailResponseDTO.setPrivateEnterpriseStatus(0);
            comDistributionDetailResponseDTO.setRelationEnterprises(Collections.emptyList());
            comDistributionDetailResponseDTO.setAbsBondShortInfos(Collections.emptyList());
            return comDistributionDetailResponseDTO;
        }
        ComShortInfoDTO comShortInfoDTO = first.get();
        comDistributionDetailResponseDTO.setPrivateEnterpriseStatus(0);
        // 获取相同发行人信息
        if (Objects.equals(comShortInfoDTO.getBusinessNature(), BusinessNatureEnum.PRIVATE_ENTERPRISE.getValue())) {
            stopWatch.start();
            comDistributionDetailResponseDTO.setPrivateEnterpriseStatus(BigDecimal.ONE.intValue());
            Optional<ComRelatedPartyDTO> comRelatedPartyOpt = dwsPartyInfoServiceHttpService.listComRelatedPartyDTOs(Collections.singleton(comUniCode)).stream().findFirst();
            comRelatedPartyOpt.ifPresent(comRelatedPartyDTO -> {
                comDistributionDetailResponseDTO.setRelationEnterprises(
                        comBasicInfoService.listComShortInfoByUniCodes(comRelatedPartyDTO.getRelaComUniCodes().toArray(new Long[0])).stream()
                                .map(v -> {
                                    RelationComShortInfoDTO relationComShortInfoDTO = new RelationComShortInfoDTO();
                                    relationComShortInfoDTO.setComUniCode(v.getComUniCode());
                                    relationComShortInfoDTO.setComFullName(v.getComFullName());
                                    return relationComShortInfoDTO;
                                }).collect(Collectors.toList()));
                comUniCodes.addAll(comRelatedPartyDTO.getRelaComUniCodes());
            });
            stopWatch.stop();
            log.info("BondsInCirculationServiceImpl#getComDistributionDetailResponseDTO 获取相同实控人信息耗时:{}ms", stopWatch.getLastTaskTimeMillis());
        }
        // 获取ABS信息
        AbsComBondDetailBaseInfoRequestDTO requestDTO = new AbsComBondDetailBaseInfoRequestDTO();
        requestDTO.setComUniCodes(Collections.singletonList(comUniCode));
        requestDTO.setAbsPartyTypes(ABS_PARTY_TYPES);
        stopWatch.start();
        List<AbsBondComDetailInfoDTO> absBondComDetailInfoDTOS = onshoreBondAbsHttpService.listAbsAbsBondComDetailInfoDTOResponseDTOs(requestDTO);
        Map<Long, AbsBondShortInfoDTO> absBondShortInfoMap = absBondComDetailInfoDTOS.stream()
                .map(v -> {
                    AbsBondShortInfoDTO absBondShortInfoDTO = new AbsBondShortInfoDTO();
                    absBondShortInfoDTO.setBondUniCode(v.getBondUniCode());
                    absBondShortInfoDTO.setBondFullName(v.getBondFullName());
                    absBondShortInfoDTO.setBondShortName(v.getBondShortName());
                    absBondShortInfoDTO.setComUniCode(v.getComUniCode());
                    return absBondShortInfoDTO;
                })
                .filter(v -> Objects.nonNull(v.getBondUniCode()))
                .collect(Collectors.toMap(AbsBondShortInfoDTO::getBondUniCode, Function.identity(), (v1, v2) -> v2));
        comDistributionDetailResponseDTO.setAbsBondShortInfos(new ArrayList<>(absBondShortInfoMap.values()));
        stopWatch.stop();
        log.info("BondsInCirculationServiceImpl#getComDistributionDetailResponseDTO 获取ABS信息耗时:{}ms", stopWatch.getLastTaskTimeMillis());
        comUniCodes.addAll(absBondComDetailInfoDTOS.stream().map(AbsBondComDetailInfoDTO::getComUniCode).collect(Collectors.toSet()));
        // 获取债券信息
        final Map<Long, List<ComBondDetailDTO>> comBondDetailDTOMap = dwsPartyInfoServiceHttpService.listComBondDetailDTOs(comUniCodes).stream()
                .collect(Collectors.groupingBy(ComBondDetailDTO::getComUniCode));
        this.setComBondDetailDTOs(comDistributionDetailResponseDTO, comBondDetailDTOMap);
        cacheOptionalService.setIfAbsent(String.format(RedisKeyConstant.REDIS_KEY_COM_INFO_F9_KEY, comUniCode), comDistributionDetailResponseDTO,
                Long.valueOf(INT_TEN.toString()), TimeUnit.MINUTES);
        return comDistributionDetailResponseDTO;
    }

    private void setComBondDetailDTOs(ComDistributionDetailResponseDTO comDistributionDetailResponseDTO, Map<Long, List<ComBondDetailDTO>> comBondDetailDTOMap) {
        Long comUniCode = comDistributionDetailResponseDTO.getComUniCode();
        List<RelationComShortInfoDTO> relationEnterprises = comDistributionDetailResponseDTO.getRelationEnterprises();
        List<AbsBondShortInfoDTO> absBondShortInfos = comDistributionDetailResponseDTO.getAbsBondShortInfos();
        // 设置 本主体
        Optional.ofNullable(comBondDetailDTOMap.get(comUniCode)).ifPresent(e -> {
            comDistributionDetailResponseDTO.setComBondDetailDTOS(new ArrayList<>(e.stream()
                    .collect(Collectors.groupingBy(bond -> BondTypeGroupEnum.getGroupByBondType(bond.getBondType())))
                    .values().stream()
                    .filter(CollectionUtils::isNotEmpty)
                    .map(comBondDetailDTOS -> {
                        ComBondDetailDTO firstBond = BeanCopyUtils.copyProperties(comBondDetailDTOS.get(0), ComBondDetailDTO.class);
                        BondTypeGroupEnum group = BondTypeGroupEnum.getGroupByBondType(firstBond.getBondType());
                        firstBond.setBondType(group.getCode());
                        firstBond.setBondTypeDes(group.getDisplayName());
                        return firstBond;
                    })
                    .sorted(Comparator.comparing(ComBondDetailDTO::getBondType))
                    .collect(Collectors.toMap(
                            ComBondDetailDTO::getBondType,
                            Function.identity(),
                            (existing, replacement) -> replacement
                    )).values()));
        });

        if (CollectionUtils.isNotEmpty(relationEnterprises)) {
            // 相同实控人
            List<ComBondDetailDTO> relationComBondDetailDTOS = new ArrayList<>(relationEnterprises.stream()
                    .map(code -> comBondDetailDTOMap.get(code.getComUniCode()))
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.groupingBy(ComBondDetailDTO::getBondType))
                    .values().stream()
                    .map(comBondDetailDTOS -> {
                        ComBondDetailDTO firstBond = BeanCopyUtils.copyProperties(comBondDetailDTOS.get(0), ComBondDetailDTO.class);
                        BondTypeGroupEnum group = BondTypeGroupEnum.getGroupByBondType(firstBond.getBondType());
                        firstBond.setBondType(group.getCode());
                        firstBond.setBondTypeDes(group.getDisplayName());
                        return firstBond;
                    })
                    .sorted(Comparator.comparing(ComBondDetailDTO::getBondType))
                    .collect(Collectors.toMap(
                            ComBondDetailDTO::getBondType,
                            Function.identity(),
                            (existing, replacement) -> replacement
                    )).values());
            comDistributionDetailResponseDTO.setRelationComBondDetailDTOS(relationComBondDetailDTOS);
        }

        if (CollectionUtils.isNotEmpty(absBondShortInfos)) {
            // 设置 相同ABS信息
            List<ComBondDetailDTO> absComBondDetailDTOS = new ArrayList<>(absBondShortInfos.stream()
                    .map(code -> comBondDetailDTOMap.get(code.getComUniCode()))
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .collect(Collectors.groupingBy(ComBondDetailDTO::getBondType))
                    .values().stream()
                    .map(comBondDetailDTOS -> {
                        ComBondDetailDTO firstBond = BeanCopyUtils.copyProperties(comBondDetailDTOS.get(0), ComBondDetailDTO.class);
                        BondTypeGroupEnum group = BondTypeGroupEnum.getGroupByBondType(firstBond.getBondType());
                        firstBond.setBondType(group.getCode());
                        firstBond.setBondTypeDes(group.getDisplayName());
                        return firstBond;
                    })
                    .sorted(Comparator.comparing(ComBondDetailDTO::getBondType))
                    .collect(Collectors.toMap(
                            ComBondDetailDTO::getBondType,
                            Function.identity(),
                            (existing, replacement) -> replacement
                    )).values());
            comDistributionDetailResponseDTO.setAbsBondDetailDTOS(absComBondDetailDTOS);
        }
    }

    @Override
    public List<BondBalanceStatisticsResponseDTO> listBondBalanceStatistics(BondBalanceStatisticsRequestDTO requestDTO) {
        if (Objects.isNull(requestDTO.getComUniCode())
                && CollectionUtils.isEmpty(requestDTO.getRelationComUniCodes())
                && CollectionUtils.isEmpty(requestDTO.getAbsBondUniCodes())) {
            return Collections.emptyList();
        }

        List<BondBalanceStatisticsResponseDTO> response = Lists.newArrayList();
        BondBalanceStatisticsBO curComReq = BeanCopyUtils.copyProperties(requestDTO, BondBalanceStatisticsBO.class);
        curComReq.setComUniCodes(Collections.singletonList(requestDTO.getComUniCode()));
        curComReq.setBondUniCodes(Collections.emptyList());
        if (CollectionUtils.isNotEmpty(requestDTO.getBondTypes())) {
            curComReq.setBondTypes(requestDTO.getBondTypes().stream().map(BondTypeGroupEnum::getByCode).filter(Objects::nonNull)
                    .map(BondTypeGroupEnum::getBondTypes).flatMap(Collection::stream).collect(Collectors.toList()));
        }
        Optional<OnshoreBondFilterView> opt = onshoreBondFilterDAO.getOnshoreBondFilterStatistics(curComReq);
        if (opt.isPresent()) {
            response.add(convert(opt.get(), BigDecimal.ONE.intValue()));
        } else {
            BondBalanceStatisticsResponseDTO bondBalanceStatisticsResponseDTO = new BondBalanceStatisticsResponseDTO();
            bondBalanceStatisticsResponseDTO.setComRelationType(BigDecimal.ONE.intValue());
            bondBalanceStatisticsResponseDTO.setDurationCount(0);
            bondBalanceStatisticsResponseDTO.setExpiredCount(0);
            response.add(bondBalanceStatisticsResponseDTO);
        }
        // 相同实控人
        if (CollectionUtils.isNotEmpty(requestDTO.getRelationComUniCodes())) {
            BondBalanceStatisticsBO realComReq = BeanCopyUtils.copyProperties(requestDTO, BondBalanceStatisticsBO.class);
            realComReq.setComUniCodes(requestDTO.getRelationComUniCodes());
            realComReq.setBondUniCodes(Collections.emptyList());
            if (CollectionUtils.isNotEmpty(requestDTO.getBondTypes())) {
                realComReq.setBondTypes(requestDTO.getBondTypes().stream().map(BondTypeGroupEnum::getByCode).filter(Objects::nonNull)
                        .map(BondTypeGroupEnum::getBondTypes).flatMap(Collection::stream).collect(Collectors.toList()));
            }
            Optional<OnshoreBondFilterView> opt1 = onshoreBondFilterDAO.getOnshoreBondFilterStatistics(realComReq);
            if (opt1.isPresent()) {
                response.add(convert(opt1.get(), INT_TWO));
            } else {
                BondBalanceStatisticsResponseDTO bondBalanceStatisticsResponseDTO = new BondBalanceStatisticsResponseDTO();
                bondBalanceStatisticsResponseDTO.setComRelationType(INT_TWO);
                bondBalanceStatisticsResponseDTO.setDurationCount(0);
                bondBalanceStatisticsResponseDTO.setExpiredCount(0);
                response.add(bondBalanceStatisticsResponseDTO);
            }
        }
        // ABS
        if (CollectionUtils.isNotEmpty(requestDTO.getAbsBondUniCodes())) {
            BondBalanceStatisticsBO absReq = BeanCopyUtils.copyProperties(requestDTO, BondBalanceStatisticsBO.class);
            absReq.setComUniCodes(Collections.emptyList());
            absReq.setBondUniCodes(requestDTO.getAbsBondUniCodes());
            if (CollectionUtils.isNotEmpty(requestDTO.getBondTypes())) {
                absReq.setBondTypes(requestDTO.getBondTypes().stream().map(BondTypeGroupEnum::getByCode).filter(Objects::nonNull)
                        .map(BondTypeGroupEnum::getBondTypes).flatMap(Collection::stream).collect(Collectors.toList()));
            }
            Optional<OnshoreBondFilterView> opt1 = onshoreBondFilterDAO.getOnshoreBondFilterStatistics(absReq);
            if (opt1.isPresent()) {
                response.add(convert(opt1.get(), INT_THREE));
            } else {
                BondBalanceStatisticsResponseDTO bondBalanceStatisticsResponseDTO = new BondBalanceStatisticsResponseDTO();
                bondBalanceStatisticsResponseDTO.setComRelationType(INT_THREE);
                bondBalanceStatisticsResponseDTO.setDurationCount(0);
                bondBalanceStatisticsResponseDTO.setExpiredCount(0);
                response.add(bondBalanceStatisticsResponseDTO);
            }
        }
        return response;
    }


    private BondBalanceStatisticsResponseDTO convert(OnshoreBondFilterView view, Integer comRelationType) {
        BondBalanceStatisticsResponseDTO response = new BondBalanceStatisticsResponseDTO();
        response.setComRelationType(comRelationType);
        response.setDurationCount(view.getDurationCount());
        response.setDurationTotalBondBalance(view.getDurationTotalBondBalance());
        response.setExpiredCount(view.getExpiredCount());
        response.setExpiredTotalBondBalance(view.getExpiredTotalBondBalance());
        return response;
    }

    private BondDetailInfoExportResponseDTO convertExportDto(BondDetailInfoPageResponseDTO item) {
        BondDetailInfoExportResponseDTO response = new BondDetailInfoExportResponseDTO();
        EnumUtils.of(PublicOffering.class, item.getPublicOffering()).map(PublicOffering::getText).ifPresent(response::setPublicOffering);
        response.setBondShortName(item.getBondShortName());
        response.setBondCode(item.getBondCode());
        response.setComFullName(item.getComFullName());
        response.setDisplayBondType(item.getDisplayBondType());
        response.setIssueStartDate(item.getIssueStartDate());
        response.setIssuePrice(item.getIssuePrice());
        response.setListDate(item.getListDate());
        EnumUtils.of(SecondMarket.class, item.getSecondMarket()).map(SecondMarket::getText).ifPresent(response::setSecondMarket);
        response.setRemainingTenor(item.getRemainingTenor());
        response.setBondBalance(item.getBondBalance());
        response.setMaturityDate(item.getMaturityDate());
        response.setExerciseDate(item.getExerciseDate());
        response.setLatestCouponRate(item.getLatestCouponRate());
        response.setLeadUnderwriter(item.getLeadUnderwriter());
        response.setRegNoticeNumber(item.getRegNoticeNumber());
        response.setCapitalCollectionUsage(item.getCapitalCollectionUsage());
        response.setGuarantor(item.getGuarantor());
        response.setOriginalEquityHolderName(item.getOriginalEquityHolderName());
        response.setCreditSubjectName(item.getCreditSubjectName());
        response.setAbsFullName(item.getAbsFullName());
        response.setAbsShortName(item.getAbsShortName());
        response.setAssetStructureTypeName(item.getAssetStructureTypeName());
        response.setProductTypeName(item.getProductTypeName());
        response.setLegalMaturityDate(item.getMaturityDate());
        response.setBondExtRatingMapping(
                Objects.toString(item.getComExtRatingMapping(), "--") + "/" + Objects.toString(item.getBondExtRatingMapping(), "--"));
        response.setBondImpliedRating(
                Objects.equals(item.getBondImpliedRating(), DisplayStringNoPermissionsJsonSerializer.NOT_AUTH_FLAG)
                        ? "**" : Objects.toString(item.getBondImpliedRating(), "--"));
        response.setCbYte(
                Joiner.on("/").join(
                        Objects.equals(item.getCbYte(), DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG)
                                ? "**" : Objects.toString(item.getCbYte(), "--"),
                        Objects.equals(item.getCbYtm(), DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG)
                                ? "**" : Objects.toString(item.getCbYtm(), "--")));
        response.setCsYte(
                Joiner.on("/").join(
                        Objects.equals(item.getCsYte(), DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG)
                                ? "**" : Objects.toString(item.getCsYte(), "--"),
                        Objects.equals(item.getCsYtm(), DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG)
                                ? "**" : Objects.toString(item.getCsYtm(), "--"))
        );
        return response;
    }


    private BondDetailInfoPageResponseDTO convert(OnshoreBondFilterDO item,
                                                  Map<Long, List<AbsBondDetailInfoDTO>> absBondDetailInfoMap,
                                                  boolean cbPermission,
                                                  boolean csPermission,
                                                  boolean cbBondImpliedRatingPermission) {
        BondDetailInfoPageResponseDTO response = new BondDetailInfoPageResponseDTO();
        response.setPublicOffering(item.getPublicOffering());
        response.setBondShortName(item.getBondShortName());
        response.setBondCode(item.getBondCode());
        response.setComUniCode(item.getComUniCode());
        response.setComFullName(item.getComFullName());
        response.setIssueStartDate(item.getIssueStartDate());
        response.setIssuePrice(item.getIssuePrice());
        response.setListDate(item.getListDate());
        response.setSecondMarket(item.getSecondMarket());
        response.setRemainingTenor(item.getRemainingTenor());
        response.setBondBalance(item.getBondBalance());
        response.setMaturityDate(item.getMaturityDate());
        response.setExerciseDate(item.getExerciseDate());
        response.setLatestCouponRate(item.getLatestCouponRate());
        response.setRegNoticeNumber(item.getRegNoticeNumber());
        response.setCapitalCollectionUsage(item.getCapitalCollectionUsage());
        response.setGuarantor(item.getGuarantor());
        response.setComExtRatingMapping(item.getComExtRating());
        response.setBondExtRatingMapping(item.getBondExtRating());
        response.setBondImpliedRating(cbBondImpliedRatingPermission ? item.getBondImpliedRating() : DisplayStringNoPermissionsJsonSerializer.NOT_AUTH_FLAG);
        response.setCbYte(cbPermission ? item.getCbYte() : DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG);
        response.setCbYtm(cbPermission ? item.getCbYtm() : DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG);
        response.setCsYte(csPermission ? item.getCsYte() : DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG);
        response.setCsYtm(csPermission ? item.getCsYtm() : DisplayNumberFormat2ScaleJsonSerializer.NOT_AUTH_FLAG);
        response.setBondUniCode(item.getBondUniCode());
        response.setBondType(item.getBondType());
        response.setRemainingTenorDay(item.getRemainingTenorDay());
        Optional.ofNullable(BondTypeGroupEnum.getGroupByBondType(item.getBondType()))
                .ifPresent(bondType -> response.setDisplayBondType(bondType.getDisplayName()));
        response.setLeadUnderwriter(item.getLeadUnderwriter());

        // abs
        Optional<AbsBondDetailInfoDTO> absOpt = Optional.ofNullable(absBondDetailInfoMap.get(item.getBondUniCode()))
                .map(e -> e.get(0));
        absOpt.ifPresent(v -> {
            response.setAbsFullName(v.getAbsFullName());
            response.setAbsShortName(v.getAbsShortName());
            response.setAssetStructureType(v.getAssetStructureType());
            response.setAssetStructureTypeName(v.getAssetStructureTypeName());
            response.setProductType(v.getProductType());
            response.setProductTypeName(v.getProductTypeName());
            response.setLegalMaturityDate(v.getLegalMaturityDate());
        });
        // 拼接原始权益人
        Optional.ofNullable(absBondDetailInfoMap.get(item.getBondUniCode()))
                .ifPresent(v ->
                        response.setOriginalEquityHolderName(v.stream()
                                .filter(e -> Objects.equals(e.getAbsPartyType(), AbsPartyTypeEnum.ORIGINAL_STAKEHOLDER.getValue()))
                                .map(AbsBondDetailInfoDTO::getAbsPartyFullName)
                                .distinct()
                                .collect(Collectors.joining(StringPool.COMMA))));
        // 拼接信用主体
        List<Integer> creditSubjects = AbsPartyTypeEnum.listCreditSubjectTypeEnum().stream().map(AbsPartyTypeEnum::getValue).collect(Collectors.toList());
        Optional.ofNullable(absBondDetailInfoMap.get(item.getBondUniCode()))
                .ifPresent(v ->
                        response.setCreditSubjectName(v.stream()
                                .filter(e -> creditSubjects.contains(e.getAbsPartyType()))
                                .map(AbsBondDetailInfoDTO::getAbsPartyFullName)
                                .distinct()
                                .collect(Collectors.joining(StringPool.COMMA))));
        return response;
    }

    private Map<Long, List<AbsBondDetailInfoDTO>> collectAbsBondDetailInfoMap(List<Long> bondUniCodes) {
        AbsBondDetailBaseInfoRequestDTO request = new AbsBondDetailBaseInfoRequestDTO();
        request.setBondUniCodes(bondUniCodes);
        request.setAbsPartyTypes(SEARCH_ABS_PARTY_TYPES);
        return onshoreBondAbsHttpService.listAbsBondDetailInfoResponseDTOs(request).stream()
                .collect(Collectors.groupingBy(AbsBondDetailInfoDTO::getBondUniCode));
    }


    /**
     * 按债券类型分组统计
     */
    private List<BondCashDistributionPieChartResponseDTO> groupByBondType(Map<Long, OnshoreBondFilterDO> bondFilterDOMap,
                                                                          Long comUniCode) {
        return bondFilterDOMap.values().stream()
                // 按照债券类型分组枚举进行分组
                .collect(Collectors.groupingBy(bond -> BondTypeGroupEnum.getGroupByBondType(bond.getBondType())))
                .entrySet().stream()
                .map(entry -> {
                    BondTypeGroupEnum typeGroup = entry.getKey();
                    BondCashDistributionPieChartResponseDTO responseDTO = new BondCashDistributionPieChartResponseDTO();
                    responseDTO.setComUniCode(comUniCode);
                    responseDTO.setFilterType(1);
                    responseDTO.setAggregationType(typeGroup.getCode());
                    responseDTO.setAggregationTypeDesc(typeGroup.getDisplayName());
                    // 计算该类型债券的总剩余规模
                    BigDecimal totalAmount = this.calculateTotalRemainAmount(entry.getValue());
                    responseDTO.setRemainAmount(totalAmount);
                    responseDTO.setBondCount(entry.getValue().size());
                    return responseDTO;
                })
                .filter(responseDTO -> responseDTO.getRemainAmount().compareTo(BigDecimal.ZERO) > 0)
                .sorted(Comparator.comparing(BondCashDistributionPieChartResponseDTO::getAggregationType))
                .collect(Collectors.toList());
    }

    /**
     * 按剩余期限分组统计
     */
    private List<BondCashDistributionPieChartResponseDTO> groupByRemainingTenor(Map<Long, OnshoreBondFilterDO> bondFilterDOMap,
                                                                                Long comUniCode) {
        return bondFilterDOMap.values().stream()
                .filter(bond -> Objects.nonNull(bond.getRemainingTenorDay()) && bond.getRemainingTenorDay() >= 0)
                .collect(Collectors.groupingBy(bond -> RemainingTenorGroupEnum.getGroupByRemainingDays(bond.getRemainingTenorDay())))
                .entrySet().stream()
                .filter(entry -> Objects.nonNull(entry.getKey()))
                .map(entry -> {
                    RemainingTenorGroupEnum tenorGroup = entry.getKey();
                    BondCashDistributionPieChartResponseDTO responseDTO = new BondCashDistributionPieChartResponseDTO();
                    responseDTO.setComUniCode(comUniCode);
                    responseDTO.setFilterType(2);
                    responseDTO.setAggregationType(tenorGroup.getCode());
                    responseDTO.setAggregationTypeDesc(tenorGroup.getDisplayName());
                    // 计算该期限段债券的总剩余规模
                    BigDecimal totalAmount = this.calculateTotalRemainAmount(entry.getValue());
                    responseDTO.setRemainAmount(totalAmount);
                    responseDTO.setBondCount(entry.getValue().size());
                    return responseDTO;
                })
                .filter(responseDTO -> responseDTO.getRemainAmount().compareTo(BigDecimal.ZERO) > 0)
                .collect(Collectors.toList());
    }

    /**
     * 计算债券列表的总剩余规模
     */
    private BigDecimal calculateTotalRemainAmount(List<OnshoreBondFilterDO> bonds) {
        return bonds.stream()
                .filter(Objects::nonNull)
                .map(OnshoreBondFilterDO::getBondBalance)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private List<Long> listCacheAbsBondUniCodes(Long comUniCode) {
        List<AbsBondComDetailInfoDTO> list = cacheOptionalService.list(String.format(REDIS_KEY_ABS_BOND_UNI_CODE_KEY, comUniCode), AbsBondComDetailInfoDTO.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .map(AbsBondComDetailInfoDTO::getBondUniCode)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
        }
        AbsComBondDetailBaseInfoRequestDTO requestDTO = new AbsComBondDetailBaseInfoRequestDTO();
        requestDTO.setComUniCodes(Collections.singletonList(comUniCode));
        requestDTO.setAbsPartyTypes(ABS_PARTY_TYPES);
        List<AbsBondComDetailInfoDTO> absBondComDetailInfoDTOS = onshoreBondAbsHttpService.listAbsAbsBondComDetailInfoDTOResponseDTOs(requestDTO);
        List<Long> collect = absBondComDetailInfoDTOS.stream()
                .map(AbsBondComDetailInfoDTO::getBondUniCode)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        cacheOptionalService.setIfAbsent(String.format(REDIS_KEY_ABS_BOND_UNI_CODE_KEY, comUniCode), absBondComDetailInfoDTOS,
                Long.valueOf(INT_TEN.toString()), TimeUnit.MINUTES);
        return collect;
    }

    /**
     * 根据用户id导出操作
     *
     * @param response          响应流
     * @param excelResponseDTOS 导出数据
     * @param excelName         excelName
     * @param excelClass        数据类
     * @param tableKey          用户缓存列key
     * @param userId            用户id
     * @throws IOException io异常
     */
    public <T> void exportOperationByUserId(HttpServletResponse response,
                                            List<T> excelResponseDTOS,
                                            String excelName,
                                            Class<T> excelClass,
                                            Long userId,
                                            String tableKey,
                                            String defaultColumns,
                                            String sortDescriptor) throws IOException {
        // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码 当然和easyExcel没有关系
        String fileName = URLEncoder.encode(excelName, "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        // 读取用户配置
        String userTableHeaderJson = StringUtils.isBlank(tableKey) ? defaultColumns : userService.getUserTableHeaderJson(userId, tableKey);
        Map<String, Integer> fieldToIndexMap = UserColumnUtil.getFieldToIndexMap(userTableHeaderJson);
        if (MapUtils.isNotEmpty(fieldToIndexMap)) {
            EasyExcel.write(response.getOutputStream(), excelClass).sheet(fileName)
                    .automaticMergeHead(true)
                    .registerWriteHandler(new DynamicColumnSortingTemplateWriteHandler(TABLE_TITLE + LocalDate.now() + sortDescriptor, fieldToIndexMap))
                    .registerWriteHandler(new CustomNullValueHandler())
                    .includeColumnFiledNames(fieldToIndexMap.keySet())
                    .registerConverter(new StringToSqlDateConvert())
                    .sheetName(excelName)
                    .doWrite(excelResponseDTOS);
            return;
        }
        EasyExcel.write(response.getOutputStream(), excelClass)
                .registerWriteHandler(new DefaultTemplateWriteHandler(TABLE_TITLE + LocalDate.now(), excelName))
                .registerWriteHandler(new CustomNullValueHandler())
                .registerConverter(new StringToSqlDateConvert())
                .sheet(excelName)
                .doWrite(excelResponseDTOS);
    }

    private List<Long> listCacheRelatedPartyComUniCodes(Long comUniCode) {
        List<ComRelatedPartyDTO> list = cacheOptionalService.list(String.format(REDIS_KEY_RELATED_PARTY_COM_UNI_CODE_KEY, comUniCode), ComRelatedPartyDTO.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .map(ComRelatedPartyDTO::getRelaComUniCodes)
                    .flatMap(Collection::stream)
                    .distinct().collect(Collectors.toList());
        }
        List<ComRelatedPartyDTO> comRelatedPartyDTOS = dwsPartyInfoServiceHttpService.listComRelatedPartyDTOs(Collections.singleton(comUniCode));
        cacheOptionalService.setIfAbsent(String.format(REDIS_KEY_RELATED_PARTY_COM_UNI_CODE_KEY, comUniCode), comRelatedPartyDTOS,
                Long.valueOf(INT_TEN.toString()), TimeUnit.MINUTES);
        return comRelatedPartyDTOS.stream()
                .map(ComRelatedPartyDTO::getRelaComUniCodes)
                .flatMap(Collection::stream)
                .distinct().collect(Collectors.toList());
    }
}
