package com.innodealing.onshore.bondservice.service.processor.factory;

import com.innodealing.onshore.bondservice.service.processor.calculator.BondCashDetailCalculator;
import com.innodealing.onshore.bondservice.service.processor.calculator.BondCashDetailCalculatorChain;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 现金流明细计算器工厂
 * 使用工厂模式创建和管理计算器
 * 支持Spring容器自动发现计算器
 *
 * <AUTHOR>
 */
@Component
public class BondCashDetailCalculatorFactory {

    private static final Logger logger = LoggerFactory.getLogger(BondCashDetailCalculatorFactory.class);

    @Resource
    private ApplicationContext applicationContext;
    @Resource
    private BondCashDetailCalculatorChain bondCashDetailCalculatorChain;
    /**
     * 所有可用的计算器
     */
    private final List<BondCashDetailCalculator> availableCalculators = new CopyOnWriteArrayList<>();

    /**
     * 初始化方法
     */
    @PostConstruct
    public void init() {
        this.discoverCalculatorsFromSpring();
        // 添加所有可用的计算器
        for (BondCashDetailCalculator calculator : availableCalculators) {
            bondCashDetailCalculatorChain.addCalculator(calculator);
        }
        logger.info("Initialized BondCashDetailCalculatorFactory with {} calculators", availableCalculators.size());
    }

    /**
     * 从Spring容器中发现计算器
     */
    private void discoverCalculatorsFromSpring() {
        try {
            Map<String, BondCashDetailCalculator> calculatorBeans = applicationContext.getBeansOfType(BondCashDetailCalculator.class);
            for (Map.Entry<String, BondCashDetailCalculator> entry : calculatorBeans.entrySet()) {
                BondCashDetailCalculator calculator = entry.getValue();
                availableCalculators.add(calculator);
                logger.info("Discovered calculator from Spring: {} ({})", entry.getKey(), calculator.getName());
            }
        } catch (Exception e) {
            logger.warn("Failed to discover calculators from Spring context", e);
        }
    }


    /**
     * 创建计算器链
     *
     * @return 配置好的计算器链
     */

    public BondCashDetailCalculatorChain getBondCashDetailCalculatorChain() {
        return bondCashDetailCalculatorChain;
    }

    /**
     * 根据名称查找计算器
     *
     * @param calculatorName 计算器名称
     * @return 计算器实例，如果不存在则返回null
     */
    public BondCashDetailCalculator findCalculatorByName(String calculatorName) {
        return availableCalculators.stream()
                .filter(calculator -> calculator.getName().equals(calculatorName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有可用的计算器名称
     *
     * @return 计算器名称列表
     */
    public List<String> getAvailableCalculatorNames() {
        List<String> names = new ArrayList<>();
        for (BondCashDetailCalculator calculator : availableCalculators) {
            names.add(calculator.getName());
        }
        return names;
    }

    /**
     * 注册新的计算器
     *
     * @param calculator 计算器实例
     */
    public void registerCalculator(BondCashDetailCalculator calculator) {
        if (calculator != null && !containsCalculator(calculator.getName())) {
            availableCalculators.add(calculator);
            logger.info("Registered new calculator: {}", calculator.getName());
        }
    }

    /**
     * 注销计算器
     *
     * @param calculatorName 计算器名称
     */
    public void unregisterCalculator(String calculatorName) {
        boolean removed = availableCalculators.removeIf(calculator -> calculator.getName().equals(calculatorName));
        if (removed) {
            logger.info("Unregistered calculator: {}", calculatorName);
        } else {
            logger.warn("Calculator not found for unregistration: {}", calculatorName);
        }
    }

    /**
     * 检查是否包含指定名称的计算器
     *
     * @param calculatorName 计算器名称
     * @return true if 包含
     */
    public boolean containsCalculator(String calculatorName) {
        return availableCalculators.stream().anyMatch(calculator -> calculator.getName().equals(calculatorName));
    }

    /**
     * 获取计算器数量
     *
     * @return 计算器数量
     */
    public int getCalculatorCount() {
        return availableCalculators.size();
    }

    /**
     * 获取工厂信息
     *
     * @return 工厂信息字符串
     */
    public String getFactoryInfo() {
        StringBuilder info = new StringBuilder();
        info.append("BondCashDetailCalculatorFactory Info:\n");
        info.append("  Total calculators: ").append(availableCalculators.size()).append("\n");
        info.append("  Available calculators:\n");
        for (BondCashDetailCalculator calculator : availableCalculators) {
            info.append(String.format("    - %s (priority: %d)\n", calculator.getName(), calculator.getPriority()));
        }
        return info.toString();
    }
}
