package com.innodealing.onshore.bondservice.utils;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.innodealing.commons.json.JSON;
import com.innodealing.onshore.bondservice.model.bo.OptionalBO;
import com.innodealing.onshore.bondservice.model.bo.TableHeaderJsonBO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用戶配置列util
 *
 * <AUTHOR>
 * @create: 2024-12-02
 */
public final class UserColumnUtil {
    private static final Logger logger = LoggerFactory.getLogger(UserColumnUtil.class);
    private static final Set<String> SERIAL_NUMBER_STR = Sets.newHashSet("序号", "公告", "标书");

    private UserColumnUtil() {
    }

    /**
     * 通过json 获取用户配置列
     *
     * @param json json
     * @return 用户配置列
     */
    public static Map<String, Integer>  getFieldToIndexMap(String json) {
        if (StringUtils.isBlank(json)) {
            return Maps.newHashMap();
        }
        try {
            Map<String, Integer> fieldToIndexMap = Maps.newHashMap();
            TableHeaderJsonBO tableHeaderJsonBO = JSON.parseObject(json, TableHeaderJsonBO.class);
            List<OptionalBO> optionalBOS = tableHeaderJsonBO.getOptions().stream().filter(v -> Boolean.TRUE.equals(v.isChecked()))
                    .filter(v -> StringUtils.isNotBlank(v.getValue()))
                    .filter(v -> !SERIAL_NUMBER_STR.contains(v.getName()))
                    .collect(Collectors.toList());
            for (int index = 0; index < optionalBOS.size(); index++) {
                OptionalBO optionalBO = optionalBOS.get(index);
                fieldToIndexMap.put(optionalBO.getValue(), index);
            }
            return fieldToIndexMap;
        } catch (Exception e) {
            logger.error("用户列表转换出错json:[{}],error: [{}]", json, e.getMessage());
            // 默认返回空集合
            return Maps.newHashMap();
        }
    }
}
