package com.innodealing.onshore.bondservice.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * 没有权限时 BigDecimal 序列化为 *
 *
 * <AUTHOR>
 * @create: 2023-11-06
 */
public class DisplayNumberFormat2ScaleJsonSerializer extends JsonSerializer<BigDecimal> {
    private static final String EMPTY_PLACEHOLDER = "--";
    private static final String NOT_AUTH_PLACEHOLDER = "**";
    private final String numberPattern = "0.00##";
    private final DecimalFormat df = new DecimalFormat(numberPattern);
    /**
     * 无权限 展示标识 用于序列化时屏蔽使用指定标识替换
     */
    public static final BigDecimal NOT_AUTH_FLAG = BigDecimal.valueOf(-99_99_999L);

    @Override
    public void serialize(BigDecimal number, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        String displayStr = this.getDisplayString(number);
        jsonGenerator.writeString(displayStr);
    }

    String getDisplayString(BigDecimal number) {
        if (Objects.isNull(number)) {
            return EMPTY_PLACEHOLDER;
        }
        if (NOT_AUTH_FLAG.equals(number)) {
            return NOT_AUTH_PLACEHOLDER;
        }
        DecimalFormat df = new DecimalFormat(numberPattern);
        return df.format(number);
    }

}
