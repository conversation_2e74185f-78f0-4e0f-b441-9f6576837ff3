package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.commons.object.DateExtensionUtils;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.*;
import com.innodealing.onshore.bondmetadata.tuple.Tuple2;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "onshore-bond-basic", url = "${bond.basic.api.url}", path = "/internal")
public interface BondInfoService {

    /**
     * 获取债券筛选信息-fetchCount大小不能超过500
     *
     * @param circulationStatus            存续状态 1:存续 0:非存续
     * @param lastUpdateTime               最新更新时间
     * @param lastUpdateTimeMaxBondUniCode 最新更新时间里面最大的bondUniCode
     * @param fetchCount                   获取数量
     * @return 债券筛选
     */
    @GetMapping("/bond/filter/v3/onshore/fetch")
    List<OnshoreBondFilterV3DTO> listBondFilters(
            @ApiParam("存续状态 1:存续 0:非存续") @RequestParam(required = false) Integer circulationStatus,
            @ApiParam(name = "lastUpdateTime", value = "最新更新时间", required = true) @RequestParam Timestamp lastUpdateTime,
            @ApiParam(name = "lastUpdateTimeMaxBondUniCode", value = "最新更新时间里面最大的bondUniCode", required = true) @RequestParam Long lastUpdateTimeMaxBondUniCode,
            @ApiParam(name = "fetchCount", value = "获取数量", required = true) @RequestParam Integer fetchCount);

    /**
     * 获取债券信息
     *
     * @param bondUniCodes 债券唯一代码
     * @return 债券信息
     */
    @PostMapping("bond/onshore/info/listByUniCodes")
    List<OnshoreBondInfoDTO> listBondInfos(@RequestBody Collection<Long> bondUniCodes);

    /**
     * 获取发行人信息
     *
     * @param comUniCodes 发行人唯一代码
     * @return 发行人信息
     */
    @PostMapping("com/info/short/getByUnicode")
    List<ComShortInfoDTO> listComInfos(@RequestBody Collection<Long> comUniCodes);


    /**
     * 是否是节假日
     *
     * @param date 日期
     * @return 是否节假日
     */
    @GetMapping("holiday")
    Boolean isHoliday(@RequestParam Date date);

    /**
     * 获取给定日期的下一个工作日（不包括自己）
     *
     * @param date 计算日期
     * @return 下一个工作日
     */
    @GetMapping("holiday/next/work/day")
    Date nextWorkDay(@RequestParam Date date);

    /**
     * 获取下一个工作日，（包括自己）
     *
     * @param date 日期
     * @return 获取下一个工作日
     */
    @GetMapping("holiday/get/next/work/day")
    Date getNextWorkDay(@RequestParam Date date);

    /**
     * 获取给定日期的下一个工作日（不包括自己）
     *
     * @param date 日期参数
     * @return 工作日DTO
     */
    @ApiOperation("获取给定日期的下一个工作日（不包括自己）")
    @GetMapping("/holiday/next/work/day/weekend")
    WorkDayDTO nextWorkDayWeekend(@RequestParam Date date);

    /**
     * 获取给定日期的下一个工作日（包括自己）
     *
     * @param date 日期参数
     * @return 工作日DTO
     */
    @ApiOperation("获取给定日期的下一个工作日（包括自己）")
    @GetMapping("/holiday/get/next/work/day/weekend")
    WorkDayDTO getNextWorkDayWeekend(@RequestParam Date date);

    /**
     * 获取指定日期的休假天数
     *
     * @param date 指定日期
     * @return 指定日期的休假天数
     */
    default Optional<Tuple2<Integer/*包括调休*/, Integer/*不包括调休*/>> getHolidayDayTuple(Date date) {
        WorkDayDTO nextWorkDayWeekend = getNextWorkDayWeekend(date);
        if (Objects.isNull(nextWorkDayWeekend)) {
            return Optional.empty();
        }
        long workDayHolidayDay = DateExtensionUtils.getDiffDays(date, nextWorkDayWeekend.getWorkDay());
        long workDayNotWeekendHolidayDay = DateExtensionUtils.getDiffDays(date, nextWorkDayWeekend.getWorkDayNotWeekend());
        return Optional.of(new Tuple2<>((int) workDayHolidayDay, (int) workDayNotWeekendHolidayDay));
    }

    /**
     * 获取债券筛选信息-批量查询bondUniCodes 对应的 OnshoreBondFilterV3DTO
     *
     * @param bondUniCodes bondUniCode
     * @return 债券筛选
     */
    @PostMapping("/bond/filter/v3/onshore/batch")
    List<OnshoreBondFilterV3DTO> listOnshoreBondFilterV3DTO(@RequestBody Long... bondUniCodes);

    /**
     * 债券名称转代码
     *
     * @param nameMd5s 债券名称
     * @return 债券名称转代码
     */
    @PostMapping("bond/unicode/getByName")
    List<BondNameToCodeDTO> getBondNameToCodeList(@RequestBody String... nameMd5s);


    /**
     * 批量获取债券代码
     *
     * @param bondCodes 债券代码(不带后缀)
     * @return 债券代码列表
     */
    @PostMapping("bond/bondCode/getByCode")
    List<BondCodeDTO> getBondCodeList(@RequestBody String... bondCodes);

    /**
     * 批量获取债券精简信息
     *
     * @param bondCodes 债券代码
     * @return 债券精简列表
     */
    @PostMapping("bond/info/short/getByCode")
    List<BondShortInfoDTO> getBondShortInfoListByCodes(@RequestBody String... bondCodes);

    /**
     * 批量获取债券精简信息
     *
     * @param bondUniCodes DM债券唯一编号
     * @return 债券精简列表
     */
    @PostMapping("bond/info/short/getByUniCode")
    List<BondShortInfoDTO> getBondShortInfoListByUniCodes(Long... bondUniCodes);

    /**
     * 获取债券精简信息
     *
     * @param bondUniCode DM唯一债券代码
     * @return 债券精简信息
     */
    default Optional<BondShortInfoDTO> getBondShortInfo(Long bondUniCode) {
        if (Objects.isNull(bondUniCode)) {
            return Optional.empty();
        }
        List<BondShortInfoDTO> bondShortInfoDTOList = getBondShortInfoListByUniCodes(bondUniCode);
        if (CollectionUtils.isEmpty(bondShortInfoDTOList)) {
            return Optional.empty();
        }
        return Optional.ofNullable(bondShortInfoDTOList.get(0));
    }
}
