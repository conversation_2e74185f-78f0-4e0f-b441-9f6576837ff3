package com.innodealing.onshore.bondservice.service.processor.calculator.impl;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.bondservice.config.constant.NumberConstant;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.service.processor.calculator.AbstractBondCashDetailCalculator;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import com.innodealing.onshore.bondservice.utils.BondCashCalculationUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.HUNDRED;

/**
 * 行权偿付利息 计算器
 * 负责计算行权偿付利息字段
 *
 * <AUTHOR>
 */
@Component
public class ExercisePayInterestCashCalculator extends AbstractBondCashDetailCalculator {

    @Override
    public String getName() {
        return ExercisePayInterestCashCalculator.class.getName();
    }

    @Override
    public int getPriority() {
        return NumberConstant.INT_FIVE;
    }

    @Override
    protected void doCalculate(BondProcessContext context, Long bondUniCode) {
        // 使用工具类进行数据验证
        if (!BondCashCalculationUtils.validateContextData(context, bondUniCode, ExercisePayInterestCashCalculator.class.getName())) {
            return;
        }
        BondOptionStatusDTO bondOptionStatusDTO = context.getBondOptionStatusMap().getOrDefault(bondUniCode, new BondOptionStatusDTO());
        // 含权
        if (this.isEmbeddedOptionBond(bondOptionStatusDTO)) {
            handleContainRightBond(context, bondUniCode);
            return;
        }
        handleNotContainRightBond(context, bondUniCode);
    }

    /**
     * 处理不含权债券以及非回售赎回债券
     */
    private void handleNotContainRightBond(BondProcessContext context, Long bondUniCode) {
        // 使用工具类处理不含权债券利息计算
        Map<String, List<BondAmountDTO>> sameYearMonthAmountDataList = BondCashCalculationUtils.groupBondAmountsByKey(
                context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList()),
                this::getBondAmountKey
        );

        Map<String, List<BondCashFlowViewDTO>> sameYearMonthBondCashFlowDataList = BondCashCalculationUtils.groupBondCashFlowsByKey(
                context.getBondCashFlowMap().getOrDefault(bondUniCode, Collections.emptyList()),
                this::getBondCashFlowKey
        );

        BondCashCalculationUtils.processNonRightBondInterest(
                context,
                bondUniCode,
                sameYearMonthAmountDataList,
                sameYearMonthBondCashFlowDataList,
                OnshoreBondCashDetailBO::setExercisePayInterestCash
        );
    }

    /**
     * 处理含权债券且回售赎回
     */
    private void handleContainRightBond(BondProcessContext context, Long bondUniCode) {
        // 处理历史数据
        Map<String, List<BondAmountDTO>> sameYearMonthAmountDataList = BondCashCalculationUtils.groupBondAmountsByKey(
                context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList()),
                this::getBondAmountKey
        );
        Map<String, List<BondCashFlowViewDTO>> sameYearMonthBondCashFlowDataList = BondCashCalculationUtils.filterHistoricalCashFlows(
                context.getBondCashFlowMap().getOrDefault(bondUniCode, Collections.emptyList()),
                this::getBondCashFlowKey
        );
        BondCashCalculationUtils.processNonRightBondInterest(
                context,
                bondUniCode,
                sameYearMonthAmountDataList,
                sameYearMonthBondCashFlowDataList,
                OnshoreBondCashDetailBO::setExercisePayInterestCash
        );
        // 处理未来行权数据
        Optional<BondCashFlowViewDTO> filterBondCashFlowOpt = filterExerciseBondCashFlow(context, bondUniCode);
        if (!filterBondCashFlowOpt.isPresent()) {
            logger.warn("ExercisePayInterestCashCalculator#handleContainRightBond 现金流不存在,跳过：com_uni_code:{},bond_uni_code:{}",
                    context.getComUniCode(), bondUniCode);
            return;
        }
        Date date = filterBondCashFlowOpt.get().getInterestEndDate();

        // 结息日大于等于当前日期，且小于行权那一期的现金流（可能有多条）
        Map<String, List<BondCashFlowViewDTO>> collect = context.getBondCashFlowMap().getOrDefault(bondUniCode, Collections.emptyList()).stream()
                .filter(cash -> Objects.nonNull(cash.getInterestEndDate()))
                // 过滤出大于今天的数据，并取最小结息日
                .filter(cash -> cash.getInterestEndDate().after(Date.valueOf(LocalDate.now())))
                // 且小于行权那一期的现金流
                .filter(cash -> cash.getInterestEndDate().before(date))
                .collect(Collectors.groupingBy(this::getBondCashFlowKey));
        if (MapUtils.isNotEmpty(collect)) {
            BondCashCalculationUtils.processNonRightBondInterest(
                    context,
                    bondUniCode,
                    sameYearMonthAmountDataList,
                    collect,
                    OnshoreBondCashDetailBO::setExercisePayInterestCash
            );
        }

        // 小于等于结息日的最大的变动日期，对应的剩余规模
        Optional<BondAmountDTO> bondAmountOpt = findLatestRemainAmount(context, bondUniCode, date);
        if (!bondAmountOpt.isPresent()) {
            logger.warn("ExercisePayInterestCashCalculator#handleContainRightBond 最新剩余规模不存在,跳过：com_uni_code:{},bond_uni_code:{}",
                    context.getComUniCode(), bondUniCode);
            return;
        }
        // 结息日+1天
        OnshoreBondCashDetailBO cashDetail = context.getOrCreateCashDetail(bondUniCode, Date.valueOf(date.toLocalDate().plusDays(1)));
        // 偿付利息计算：利息支付 * 剩余规模 / 100
        BigDecimalUtils.safeMultiply(filterBondCashFlowOpt.get().getInterestPayment(), bondAmountOpt.get().getRemainAmount())
                .flatMap(v -> BigDecimalUtils.safeDivide(v, HUNDRED, RoundingMode.HALF_UP))
                .ifPresent(cashDetail::setExercisePayInterestCash);
    }

}
