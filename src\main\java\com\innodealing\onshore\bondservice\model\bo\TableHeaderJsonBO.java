package com.innodealing.onshore.bondservice.model.bo;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 *用户表头json
 *
 * <AUTHOR>
 * @create: 2024-12-02
 */
public class TableHeaderJsonBO {
    @JsonProperty("options")
    private List<OptionalBO> options;
    @JsonProperty("version")
    private String version;

    public List<OptionalBO> getOptions() {
        return Objects.isNull(options) ? new ArrayList<>() : new ArrayList<>(options);
    }

    public void setOptions(List<OptionalBO> options) {
        this.options = Objects.isNull(options) ? new ArrayList<>() : new ArrayList<>(options);
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
}
