package com.innodealing.onshore.bondservice.config.rest;

import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;


/**
 * RestTemplateConfig
 *
 * <AUTHOR>
 * @createTime 2025/1/8 18:26
 */
@Configuration
public class RestTemplateConfig {
    private static final int CONNECT_TIME_OUT_MILLIS = 600_000;
    private static final int READ_TIME_OUT_MILLIS = 600_000;
    private static final int MAX_TOTAL = 500;
    private static final int MAX_PER_ROUTE = 300;

    /**
     * rest template
     *
     * @param factory rest template factory
     * @return {@link RestTemplate}
     */
    @Bean
    public RestTemplate restTemplate(ClientHttpRequestFactory factory) {
        return new RestTemplate(factory);
    }


    /**
     * http client factory
     *
     * @param httpClient http client
     * @return {@link ClientHttpRequestFactory}
     */
    @Bean
    public ClientHttpRequestFactory httpRequestFactory(@Qualifier("customHttpClient") HttpClient httpClient) {
        return new HttpComponentsClientHttpRequestFactory(httpClient);
    }

    /**
     * http client
     *
     * @return {@link HttpClient}
     */
    @Bean
    public HttpClient customHttpClient() {
        Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                .register("http", PlainConnectionSocketFactory.getSocketFactory())
                .register("https", SSLConnectionSocketFactory.getSocketFactory())
                .build();
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
        //设置整个连接池最大连接数
        connectionManager.setMaxTotal(MAX_TOTAL);
        //MaxPerRoute路由是对maxTotal的细分,每个主机的并发，这里route指的是域名
        connectionManager.setDefaultMaxPerRoute(MAX_PER_ROUTE);
        RequestConfig requestConfig = RequestConfig.custom()
                //返回数据的超时时间
                .setSocketTimeout(READ_TIME_OUT_MILLIS)
                //连接上服务器的超时时间
                .setConnectTimeout(CONNECT_TIME_OUT_MILLIS)
                //从连接池中获取连接的超时时间
                .setConnectionRequestTimeout(READ_TIME_OUT_MILLIS)
                .build();
        return HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .setConnectionManager(connectionManager)
                .build();
    }
}
