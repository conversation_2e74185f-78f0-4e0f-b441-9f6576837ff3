package com.innodealing.onshore.bondservice.service.impl;

import com.innodealing.onshore.bondservice.dao.pgbond.mv.MvOnshoreBondCashDetailMonthDAO;
import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailMonthGroupDO;
import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;
import com.innodealing.onshore.bondservice.service.MvOnshoreBondCashDetailStrategy;
import jodd.util.StringPool;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 月度数据查询策略实现
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@Component
public class MvOnshoreMonthCashDetailStrategy implements MvOnshoreBondCashDetailStrategy {

    @Resource
    private MvOnshoreBondCashDetailMonthDAO mvOnshoreBondCashDetailMonthDAO;

    @Override
    public DateTypeEnum getSupportedDateType() {
        return DateTypeEnum.MONTH;
    }

    @Override
    public List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataNoExercise(Set<Long> comUniCodes) {
        List<MvOnshoreBondCashDetailMonthGroupDO> mvMonthData = mvOnshoreBondCashDetailMonthDAO.listMvOnshoreBondCashDetailMonthGroupDO(comUniCodes);
        return convertToBondCashPayInfoDTO(mvMonthData);
    }

    @Override
    public List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataExercise(Set<Long> comUniCodes) {
        List<MvOnshoreBondCashDetailMonthGroupDO> mvMonthData = mvOnshoreBondCashDetailMonthDAO.listMvOnshoreBondCashDetailMonthGroupDO(comUniCodes);
        return convertToExerciseBondCashPayInfoDTO(mvMonthData);
    }

    /**
     * 转换为普通债券现金流DTO
     */
    private List<BondCashPayInfoDTO> convertToBondCashPayInfoDTO(List<MvOnshoreBondCashDetailMonthGroupDO> dataList) {
        return dataList.stream().map(data -> {
            BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
            dto.setDataYear(data.getDataYear());
            dto.setDataMonth(data.getDataMonth());
            dto.setDataDateStr(String.format("%d-%02d", data.getDataYear(), data.getDataMonth()));
            dto.setPayInterestCash(data.getTotalPayInterestCash());
            dto.setPayPrincipalCash(data.getTotalPayPrincipalCash());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为行权债券现金流DTO
     */
    private List<BondCashPayInfoDTO> convertToExerciseBondCashPayInfoDTO(List<MvOnshoreBondCashDetailMonthGroupDO> dataList) {
        return dataList.stream().map(data -> {
            BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
            dto.setDataYear(data.getDataYear());
            dto.setDataMonth(data.getDataMonth());
            dto.setDataDateStr(String.format("%d-%02d", data.getDataYear(), data.getDataMonth()));
            dto.setPayInterestCash(data.getTotalExercisePayInterestCash());
            dto.setPayPrincipalCash(data.getTotalExercisePayPrincipalCash());
            return dto;
        }).collect(Collectors.toList());
    }
} 