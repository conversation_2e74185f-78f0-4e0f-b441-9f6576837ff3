package com.innodealing.onshore.bondservice.mapper.pgbond.mv.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailMonthGroupDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.mv.MvOnshoreBondCashDetailMonthDO;

/**
 * 国内债券现金流按月统计物化视图
 *
 * <AUTHOR>
 */
public interface MvOnshoreBondCashDetailMonthGroupMapper extends SelectByGroupedQueryMapper<MvOnshoreBondCashDetailMonthDO, MvOnshoreBondCashDetailMonthGroupDO> {
}