package com.innodealing.onshore.bondservice.model.enums;

import javax.annotation.Nullable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 债券类型分组枚举
 * 用于将不同的bondType映射到统一的终端展示分类
 *
 * <AUTHOR>
 * @date 2025/01/13
 */
public enum BondTypeGroupEnum {

    TREASURY_BOND(1, "国债", Collections.singletonList(1)),
    LOCAL_GOVERNMENT_GENERAL_BOND(2, "地方政府一般债", Collections.singletonList(2)),
    LOCAL_GOVERNMENT_PROJECT_SPECIAL_BOND(3, "地方政府项目专项债", Collections.singletonList(45)),
    LOCAL_GOVERNMENT_GENERAL_SPECIAL_BOND(4, "地方政府普通专项债", Collections.singletonList(46)),
    POLICY_BANK_GENERAL_BOND(5, "政策性银行普通债券", Collections.singletonList(10)),
    POLICY_BANK_SUBORDINATED_BOND(6, "政策性银行次级债券", Collections.singletonList(11)),
    SUPER_SHORT_TERM_FINANCING(7, "超短融", Collections.singletonList(23)),
    SHORT_TERM_FINANCING(8, "短融", Collections.singletonList(9)),
    MEDIUM_TERM_NOTE(9, "中票", Collections.singletonList(8)),
    ENTERPRISE_BOND(10, "企业债", Arrays.asList(4, 21, 26)),
    CORPORATE_BOND(11, "公司债", Collections.singletonList(5)),
    CONVERTIBLE_BOND(12, "可转换债券", Arrays.asList(6, 7)),
    EXCHANGEABLE_BOND(13, "可交换债券", Collections.singletonList(32)),
    COMMERCIAL_BANK_GENERAL_BOND(14, "商业银行普通债券", Collections.singletonList(12)),
    COMMERCIAL_BANK_SUBORDINATED_BOND(15, "商业银行次级债券", Collections.singletonList(13)),
    TIER2_CAPITAL_INSTRUMENT(16, "二级资本工具", Collections.singletonList(14)),
    OTHER_FINANCIAL_BOND(17, "其他金融债", Collections.singletonList(15)),
    INSURANCE_SUBORDINATED_DEBT(18, "保险公司次级定期债务", Collections.singletonList(24)),
    SECURITIES_COMPANY_BOND(19, "证券公司债", Collections.singletonList(37)),
    SECURITIES_COMPANY_SUBORDINATED_BOND(20, "证券公司次级债", Collections.singletonList(43)),
    SECURITIES_COMPANY_SHORT_TERM_FINANCING(21, "证券公司短期融资券", Collections.singletonList(44)),
    PPN(22, "PPN", Collections.singletonList(22)),
    ABS(23, "ABS", Arrays.asList(16, 17, 18, 28, 29)),
    OTHER(99, "其他", Arrays.asList(3, 19, 20, 25, 30, 31, 33, 34, 35, 41, 42, 47));

    private final Integer code;
    private final String displayName;
    private final List<Integer> bondTypes;

    BondTypeGroupEnum(Integer code, String displayName, List<Integer> bondTypes) {
        this.code = code;
        this.displayName = displayName;
        this.bondTypes = bondTypes;
    }

    public Integer getCode() {
        return code;
    }

    public String getDisplayName() {
        return displayName;
    }

    public List<Integer> getBondTypes() {
        return bondTypes;
    }

    /**
     * 静态映射Map，用于快速查找bondType对应的分组
     */
    private static final Map<Integer, BondTypeGroupEnum> BOND_TYPE_TO_GROUP_MAP;

    static {
        BOND_TYPE_TO_GROUP_MAP = Arrays.stream(values())
                .filter(group -> group != OTHER)
                .flatMap(group -> group.getBondTypes().stream()
                        .map(bondType -> new AbstractMap.SimpleEntry<>(bondType, group)))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 根据bondType获取对应的分组
     *
     * @param bondType 债券类型编码
     * @return 对应的分组枚举，如果没有找到则返回OTHER
     */
    public static BondTypeGroupEnum getGroupByBondType(Integer bondType) {
        if (bondType == null) {
            return OTHER;
        }
        return BOND_TYPE_TO_GROUP_MAP.getOrDefault(bondType, OTHER);
    }

    /**
     * 获取所有有效的分组（排除OTHER）
     *
     * @return 有效分组列表
     */
    public static List<BondTypeGroupEnum> getValidGroups() {
        return Arrays.stream(values())
                .filter(group -> group != OTHER)
                .collect(Collectors.toList());
    }

    /**
     * 根据code获取枚举
     *
     * @param code 分组编码
     * @return 对应的枚举，如果没有找到则返回null
     */
    @Nullable
    public static BondTypeGroupEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.stream(values())
                .filter(group -> Objects.equals(group.getCode(), code))
                .findFirst()
                .orElse(null);
    }
} 