package com.innodealing.onshore.bondservice.model.dto;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayIntegerJsonSerializer;
import com.innodealing.onshore.bondservice.serializer.AbsTenThousandToBillion4ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 流通中债券-债券偿付信息
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondCashPayInfoDTO {
    @ApiModelProperty("数据年份")
    @JsonSerialize(using = DisplayIntegerJsonSerializer.class, nullsUsing = DisplayIntegerJsonSerializer.class)
    private Integer dataYear;
    @ApiModelProperty("数据月份")
    @JsonSerialize(using = DisplayIntegerJsonSerializer.class, nullsUsing = DisplayIntegerJsonSerializer.class)
    private Integer dataMonth;
    @ApiModelProperty("数据季度")
    @JsonSerialize(using = DisplayIntegerJsonSerializer.class, nullsUsing = DisplayIntegerJsonSerializer.class)
    private Integer dataQuarter;
    @ApiModelProperty("日期str")
    private String dataDateStr;
    @ApiModelProperty("偿付利息(亿)")
    @JsonSerialize(using = AbsTenThousandToBillion4ScaleJsonSerializer.class, nullsUsing = AbsTenThousandToBillion4ScaleJsonSerializer.class)
    private BigDecimal payInterestCash;
    @ApiModelProperty("偿付本金(亿)")
    @JsonSerialize(using = AbsTenThousandToBillion4ScaleJsonSerializer.class, nullsUsing = AbsTenThousandToBillion4ScaleJsonSerializer.class)
    private BigDecimal payPrincipalCash;


    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }

    public Integer getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(Integer dataMonth) {
        this.dataMonth = dataMonth;
    }

    public Integer getDataQuarter() {
        return dataQuarter;
    }

    public void setDataQuarter(Integer dataQuarter) {
        this.dataQuarter = dataQuarter;
    }

    public String getDataDateStr() {
        return dataDateStr;
    }

    public void setDataDateStr(String dataDateStr) {
        this.dataDateStr = dataDateStr;
    }

    public BigDecimal getPayInterestCash() {
        return payInterestCash;
    }

    public void setPayInterestCash(BigDecimal payInterestCash) {
        this.payInterestCash = payInterestCash;
    }

    public BigDecimal getPayPrincipalCash() {
        return payPrincipalCash;
    }

    public void setPayPrincipalCash(BigDecimal payPrincipalCash) {
        this.payPrincipalCash = payPrincipalCash;
    }
}
