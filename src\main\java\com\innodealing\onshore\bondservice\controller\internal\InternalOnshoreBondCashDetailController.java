package com.innodealing.onshore.bondservice.controller.internal;

import com.innodealing.onshore.bondservice.service.OnshoreBondCashDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 国内债券现金流水明细表控制
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2025年02月07日 11:22:00
 */
@Api(tags = "(内部)国内债券现金流水明细表控制")
@RestController
@RequestMapping("internal/onshore-bond-cash-detail")
public class InternalOnshoreBondCashDetailController {

    @Resource
    private OnshoreBondCashDetailService onshoreBondCashDetailService;

    @ApiOperation(value = "同步计算债券现金流水明细(pg)")
    @PostMapping("/sync/pg")
    public void syncOnshoreBondCashDetail(@RequestBody Set<Long> comUniCodes) {
        onshoreBondCashDetailService.syncOnshoreBondCashDetail(comUniCodes);
    }


    @ApiOperation(value = "(task)增量数据同步计算债券现金流水明细(pg)")
    @PostMapping("job/sync/pg")
    public void jobSyncOnshoreBondCashDetail() {
        onshoreBondCashDetailService.jobSyncOnshoreBondCashDetail();
    }


    @ApiOperation(value = "同步计算单个债券现金流水明细(pg)")
    @PostMapping("/sync/pg/single")
    public void handleSingleOnshoreBondCashDetail(@RequestParam Long comUniCode,
                                                  @RequestParam Long bondUniCode) {
        onshoreBondCashDetailService.handleSingleOnshoreBondCashDetail(comUniCode, bondUniCode);
    }

    @ApiOperation("(task)刷新债券现金流水物化视图")
    @PostMapping("/job/refresh/mv")
    public void refreshMaterializedView() {
        onshoreBondCashDetailService.refreshMaterializedView();
    }

    @ApiModelProperty("删除全量数据")
    @DeleteMapping("/delete/all/pg")
    public void deleteAllOnshoreBondCashDetail(@ApiParam("是否全量删除数据 1:删除 0:不删除") @RequestParam Integer deleted) {
        if (deleted == 1) {
            onshoreBondCashDetailService.deleteAllOnshoreBondCashDetail();
        }
    }
}
