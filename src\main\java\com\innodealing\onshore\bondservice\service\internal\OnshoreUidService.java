package com.innodealing.onshore.bondservice.service.internal;

import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 分布式id service
 *
 * <AUTHOR>
 * @create: 2024-03-18
 */
@FeignClient(name = "onshore-uid-service", url = "${onshore.uid.service.url}", path = "internal")
public interface OnshoreUidService {
    /**
     * 获取uid
     *
     * @param serviceKey 服务key
     * @return uid
     */
    @GetMapping("/generator/uid")
    Long getUid(@ApiParam(name = "serviceKey", value = "业务key", required = true) @RequestParam("serviceKey") String serviceKey);

    /**
     * 批量生成uid
     *
     * @param serviceKey 服务key
     * @return uid
     */
    @GetMapping("/generator/listUid")
    List<Long> listUid(@ApiParam(name = "serviceKey", value = "业务key", required = true) @RequestParam("serviceKey") String serviceKey,
                       @ApiParam(name = "batchSize", value = "数量", required = true) @RequestParam("batchSize") Integer batchSize);
}
