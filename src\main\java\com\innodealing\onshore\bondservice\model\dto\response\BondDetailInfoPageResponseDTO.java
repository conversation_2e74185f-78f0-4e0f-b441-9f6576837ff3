package com.innodealing.onshore.bondservice.model.dto.response;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.*;
import com.innodealing.onshore.bondservice.serializer.DisplayNumberFormat2ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondDetailInfoPageResponseDTO {
    @ApiModelProperty("募集: 1: 公募; 0: 私募")
    private Integer publicOffering;
    @ApiModelProperty("债券唯一编码")
    private Long bondUniCode;
    @ApiModelProperty("债券简称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String bondShortName;
    @ApiModelProperty("债券代码（外部）")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String bondCode;
    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("主体全称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String comFullName;
    @ApiModelProperty("债券类型")
    private Integer bondType;
    @ApiModelProperty("展示债券类型")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String displayBondType;
    @ApiModelProperty("发行日")
    @JsonSerialize(using = DisplayDateJsonWithFormatSerializer.class, nullsUsing = DisplayDateJsonWithFormatSerializer.class)
    private Date issueStartDate;
    @ApiModelProperty("发行价")
    @JsonSerialize(using = DisplayNumberFormat2ScaleJsonSerializer.class, nullsUsing = DisplayNumberFormat2ScaleJsonSerializer.class)
    private BigDecimal issuePrice;
    @ApiModelProperty("上市日期")
    @JsonSerialize(using = DisplayDateJsonWithFormatSerializer.class, nullsUsing = DisplayDateJsonWithFormatSerializer.class)
    private Date listDate;
    @ApiModelProperty("交易场所  1 深圳证券交易所;2 上海证券交易所; 3 银行间市场;4 柜台交易市场; 99 其他")
    private Integer secondMarket;
    @ApiModelProperty("剩余期限")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String remainingTenor;
    @ApiModelProperty("剩余期限(天数)")
    @JsonSerialize(using = DisplayNumberJsonSerializer.class, nullsUsing = DisplayNumberJsonSerializer.class)
    private Integer remainingTenorDay;
    @ApiModelProperty("债券余额 (亿)")
    @JsonSerialize(using = DisplayNumber2ScaleJsonSerializer.class, nullsUsing = DisplayNumber2ScaleJsonSerializer.class)
    private BigDecimal bondBalance;
    @ApiModelProperty("到期日期")
    @JsonSerialize(using = DisplayDateJsonWithFormatSerializer.class, nullsUsing = DisplayDateJsonWithFormatSerializer.class)
    private Date maturityDate;
    @ApiModelProperty("下一行权日")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String exerciseDate;
    @ApiModelProperty("最新票面利率(%)，优先取t_bond_basic_info表new_coup_rate，若该字段为空或为0，取ref_yield")
    @JsonSerialize(using = DisplayNumberFormat2ScaleJsonSerializer.class, nullsUsing = DisplayNumberFormat2ScaleJsonSerializer.class)
    private BigDecimal latestCouponRate;
    @ApiModelProperty("主承销商")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String leadUnderwriter;
    @ApiModelProperty("注册文书号")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String regNoticeNumber;
    @ApiModelProperty("募集资金用途")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String capitalCollectionUsage;
    @ApiModelProperty("担保人")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String guarantor;
    @ApiModelProperty("币种: 1: CNY; 2: HKD; 3: USD")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String currency;
    @ApiModelProperty("主体外部评级")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String comExtRatingMapping;
    @ApiModelProperty("债券外部评级")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String bondExtRatingMapping;
    @ApiModelProperty("中债隐含评级")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String bondImpliedRating;
    @ApiModelProperty("中债行权收益率")
    @JsonSerialize(using = DisplayNumberFormat2ScaleJsonSerializer.class, nullsUsing = DisplayNumberFormat2ScaleJsonSerializer.class)
    private BigDecimal cbYte;
    @ApiModelProperty("中债到期收益率")
    @JsonSerialize(using = DisplayNumberFormat2ScaleJsonSerializer.class, nullsUsing = DisplayNumberFormat2ScaleJsonSerializer.class)
    private BigDecimal cbYtm;
    @ApiModelProperty("中证行权收益率")
    @JsonSerialize(using = DisplayNumberFormat2ScaleJsonSerializer.class, nullsUsing = DisplayNumberFormat2ScaleJsonSerializer.class)
    private BigDecimal csYte;
    @ApiModelProperty("中证到期收益率")
    @JsonSerialize(using = DisplayNumberFormat2ScaleJsonSerializer.class, nullsUsing = DisplayNumberFormat2ScaleJsonSerializer.class)
    private BigDecimal csYtm;
    @ApiModelProperty("计划管理人")
    private String planManager;

    @ApiModelProperty("原始权益人名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String originalEquityHolderName;
    @ApiModelProperty("信用主体")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String creditSubjectName;
    @ApiModelProperty("产品全称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String absFullName;
    @ApiModelProperty("产品简称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String absShortName;
    @ApiModelProperty("循环池类型")
    private Integer assetStructureType;
    @ApiModelProperty("循环池类型名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String assetStructureTypeName;
    @ApiModelProperty("基础资产分类")
    private Integer productType;
    @ApiModelProperty("基础资产分类名称")
    @JsonSerialize(using = DisplayStringJsonSerializer.class, nullsUsing = DisplayStringJsonSerializer.class)
    private String productTypeName;
    @ApiModelProperty("法定到期日")
    @JsonSerialize(using = DisplayDateJsonWithFormatSerializer.class, nullsUsing = DisplayDateJsonWithFormatSerializer.class)
    private Date legalMaturityDate;


    public Integer getPublicOffering() {
        return publicOffering;
    }

    public void setPublicOffering(Integer publicOffering) {
        this.publicOffering = publicOffering;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public String getComFullName() {
        return comFullName;
    }

    public void setComFullName(String comFullName) {
        this.comFullName = comFullName;
    }

    public Date getIssueStartDate() {
        return issueStartDate;
    }

    public void setIssueStartDate(Date issueStartDate) {
        this.issueStartDate = issueStartDate;
    }

    public BigDecimal getIssuePrice() {
        return issuePrice;
    }

    public void setIssuePrice(BigDecimal issuePrice) {
        this.issuePrice = issuePrice;
    }

    public Date getListDate() {
        return listDate;
    }

    public void setListDate(Date listDate) {
        this.listDate = listDate;
    }

    public Integer getSecondMarket() {
        return secondMarket;
    }

    public void setSecondMarket(Integer secondMarket) {
        this.secondMarket = secondMarket;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public Date getMaturityDate() {
        return maturityDate;
    }

    public void setMaturityDate(Date maturityDate) {
        this.maturityDate = maturityDate;
    }

    public String getExerciseDate() {
        return exerciseDate;
    }

    public void setExerciseDate(String exerciseDate) {
        this.exerciseDate = exerciseDate;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public String getRegNoticeNumber() {
        return regNoticeNumber;
    }

    public void setRegNoticeNumber(String regNoticeNumber) {
        this.regNoticeNumber = regNoticeNumber;
    }

    public String getCapitalCollectionUsage() {
        return capitalCollectionUsage;
    }

    public void setCapitalCollectionUsage(String capitalCollectionUsage) {
        this.capitalCollectionUsage = capitalCollectionUsage;
    }

    public String getGuarantor() {
        return guarantor;
    }

    public void setGuarantor(String guarantor) {
        this.guarantor = guarantor;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(String comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }

    public String getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(String bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public String getBondImpliedRating() {
        return bondImpliedRating;
    }

    public void setBondImpliedRating(String bondImpliedRating) {
        this.bondImpliedRating = bondImpliedRating;
    }

    public BigDecimal getCbYte() {
        return cbYte;
    }

    public void setCbYte(BigDecimal cbYte) {
        this.cbYte = cbYte;
    }

    public BigDecimal getCbYtm() {
        return cbYtm;
    }

    public void setCbYtm(BigDecimal cbYtm) {
        this.cbYtm = cbYtm;
    }

    public BigDecimal getCsYte() {
        return csYte;
    }

    public void setCsYte(BigDecimal csYte) {
        this.csYte = csYte;
    }

    public BigDecimal getCsYtm() {
        return csYtm;
    }

    public void setCsYtm(BigDecimal csYtm) {
        this.csYtm = csYtm;
    }

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Integer getBondType() {
        return bondType;
    }

    public void setBondType(Integer bondType) {
        this.bondType = bondType;
    }

    public String getDisplayBondType() {
        return displayBondType;
    }

    public void setDisplayBondType(String displayBondType) {
        this.displayBondType = displayBondType;
    }

    public String getLeadUnderwriter() {
        return leadUnderwriter;
    }

    public void setLeadUnderwriter(String leadUnderwriter) {
        this.leadUnderwriter = leadUnderwriter;
    }

    public String getPlanManager() {
        return planManager;
    }

    public void setPlanManager(String planManager) {
        this.planManager = planManager;
    }

    public String getOriginalEquityHolderName() {
        return originalEquityHolderName;
    }

    public void setOriginalEquityHolderName(String originalEquityHolderName) {
        this.originalEquityHolderName = originalEquityHolderName;
    }

    public String getAbsFullName() {
        return absFullName;
    }

    public void setAbsFullName(String absFullName) {
        this.absFullName = absFullName;
    }

    public String getAbsShortName() {
        return absShortName;
    }

    public void setAbsShortName(String absShortName) {
        this.absShortName = absShortName;
    }

    public Integer getAssetStructureType() {
        return assetStructureType;
    }

    public void setAssetStructureType(Integer assetStructureType) {
        this.assetStructureType = assetStructureType;
    }

    public String getAssetStructureTypeName() {
        return assetStructureTypeName;
    }

    public void setAssetStructureTypeName(String assetStructureTypeName) {
        this.assetStructureTypeName = assetStructureTypeName;
    }

    public Integer getProductType() {
        return productType;
    }

    public void setProductType(Integer productType) {
        this.productType = productType;
    }

    public String getProductTypeName() {
        return productTypeName;
    }

    public void setProductTypeName(String productTypeName) {
        this.productTypeName = productTypeName;
    }

    public Date getLegalMaturityDate() {
        return legalMaturityDate;
    }

    public void setLegalMaturityDate(Date legalMaturityDate) {
        this.legalMaturityDate = legalMaturityDate;
    }

    public String getCreditSubjectName() {
        return creditSubjectName;
    }

    public void setCreditSubjectName(String creditSubjectName) {
        this.creditSubjectName = creditSubjectName;
    }

    public Integer getRemainingTenorDay() {
        return remainingTenorDay;
    }

    public void setRemainingTenorDay(Integer remainingTenorDay) {
        this.remainingTenorDay = remainingTenorDay;
    }
}
