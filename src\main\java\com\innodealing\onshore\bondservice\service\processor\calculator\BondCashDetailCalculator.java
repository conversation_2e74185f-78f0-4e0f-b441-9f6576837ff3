package com.innodealing.onshore.bondservice.service.processor.calculator;

import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;

/**
 * 现金流明细计算器接口
 * 使用策略模式定义不同字段的计算策略
 * 
 * <AUTHOR>
 */
public interface BondCashDetailCalculator {
    
    /**
     * 计算指定债券的现金流明细
     * 
     * @param context 处理上下文
     * @param bondUniCode 债券唯一编码
     */
    void calculate(BondProcessContext context, Long bondUniCode);
    
    /**
     * 获取计算器名称
     * 
     * @return 计算器名称
     */
    String getName();
    
    /**
     * 获取计算器优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    int getPriority();
}
