package com.innodealing.onshore.bondservice.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.commons.skywalking.SwThreadPoolWorker;
import com.innodealing.onshore.bondmetadata.bo.rocketmq.CanalFlatMessageBO;
import com.innodealing.onshore.bondmetadata.dto.bond.valuation.CsBondValuationBusinessDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.*;
import com.innodealing.onshore.bondmetadata.dto.chinabond.DwdCbValuationDTO;
import com.innodealing.onshore.bondmetadata.dto.dmdataproduct.BondBasicDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondIssueDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondTypeDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.request.BondIssueRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.request.BondOptionRequestDTO;
import com.innodealing.onshore.bondmetadata.enums.DataOperationEnum;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondmetadata.enums.IssueAgencyTypeEnum;
import com.innodealing.onshore.bondmetadata.enums.ValuationTypeEnum;
import com.innodealing.onshore.bondservice.dao.bond.OnshoreBondFilterDAO;
import com.innodealing.onshore.bondservice.model.bo.FetchBondInfoAndPriceBO;
import com.innodealing.onshore.bondservice.model.dto.CsBondValuationMqDTO;
import com.innodealing.onshore.bondservice.model.dto.DwdCbValuationMqDTO;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.service.BondFilterService;
import com.innodealing.onshore.bondservice.service.internal.*;
import com.innodealing.onshore.bondservice.utils.ThreadPoolUtils;
import com.innodealing.onshore.service.HolidayOptionalService;
import jodd.util.StringPool;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.FIVE_HUNDRED;
import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.TWO_HUNDRED;

/**
 * 债券筛选服务
 *
 * <AUTHOR>
 */
@Service
public class BondFilterServiceImpl implements BondFilterService {

    private static final int LIMIT = 500;
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    public static final String GUAR_FULL_NAME_CONNECTOR = ",";
    public static final String ONSHORE_BOND_FILTER_SYNC_KEY = "onshore-bond-service:sync:onshore-bond-filter";
    public static final String ONSHORE_BOND_FILTER_SYNC_ID_KEY = "onshore-bond-service:sync:onshore-bond-filter:id";
    @Resource
    private OnshoreBondFilterDAO onshoreBondFilterDAO;
    @Resource
    private BondInfoService bondInfoService;
    @Resource
    private DwsBondInfoServiceHttpService dwsBondInfoServiceHttpService;
    @Resource
    private DwdBondValuationServiceHttpService dwdBondValuationServiceHttpService;
    @Resource
    private OnshoreDwdChinaBondHttpService onshoreDwdChinaBondHttpService;
    @Resource
    private DwsPartyInfoServiceHttpService dwsPartyInfoServiceHttpService;
    @Resource
    private HolidayOptionalService holidayOptionalService;

    @Resource
    private InternalKeyValueService internalKeyValueService;
    private static final LocalTime CHINA_BOND_START_TIME = LocalTime.of(18, 30, 0);
    private static final ExecutorService WORK_POOL = ThreadPoolUtils.getFixedThreadPool(
            Runtime.getRuntime().availableProcessors(), Runtime.getRuntime().availableProcessors() * 2);


    @Override
    public void syncBondFilters(@Nullable Long lastTimeStamp, @Nullable Integer deletedAll) {
        logger.info("[syncBondFilters] start");

        if (Objects.equals(deletedAll, Deleted.DELETED.getValue())) {
            onshoreBondFilterDAO.deleteAll();
            internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(ONSHORE_BOND_FILTER_SYNC_KEY, String.valueOf(0)));
            internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(ONSHORE_BOND_FILTER_SYNC_ID_KEY, String.valueOf(0)));
            logger.info("[syncBondFilters] deletedAll success");
        }
        int effectRows = 0;
        Timestamp lastUpdateTime = new Timestamp(Objects.nonNull(lastTimeStamp)
                ? lastTimeStamp
                : Optional.ofNullable(internalKeyValueService.getValue(ONSHORE_BOND_FILTER_SYNC_KEY)).map(Long::parseLong).orElse(0L));
        Long id = Optional.ofNullable(internalKeyValueService.getValue(ONSHORE_BOND_FILTER_SYNC_ID_KEY)).map(Long::parseLong).orElse(0L);

        long startBondUniCode = 0L;
        Integer fetchSize = null;
        while (Objects.isNull(fetchSize) || fetchSize == LIMIT) {
            List<OnshoreBondFilterV3DTO> onshoreBondFilterV3DTOs = bondInfoService.listBondFilters(null, lastUpdateTime, startBondUniCode, LIMIT);
            final Long currentMaxId = onshoreBondFilterV3DTOs.stream().map(OnshoreBondFilterV3DTO::getId)
                    .max(Long::compareTo).orElse(0L);
            // 获取 基础map，列表形式
            if (CollectionUtils.isEmpty(onshoreBondFilterV3DTOs) || currentMaxId.equals(id)) {
                continue;
            }
            // 通过bondUniCodes 批量再次获取 基础和价格的一些字段数据
            FetchBondInfoAndPriceBO invokeResp = invokeByBondUniCodes(onshoreBondFilterV3DTOs);
            // 转换为实体
            List<OnshoreBondFilterDO> bondFilterDOs = onshoreBondFilterV3DTOs.stream()
                    .map(bondFilter -> toEntity(bondFilter, invokeResp))
                    .collect(Collectors.toList());
            effectRows += onshoreBondFilterDAO.saveBondFilters(bondFilterDOs);
            logger.info("[syncBondFilters] success, startBondUniCode: {},lastTimeStamp:{}, effectRows: {}", startBondUniCode, lastUpdateTime, effectRows);
            final Timestamp currentMaxUpdateTime = onshoreBondFilterV3DTOs.stream().map(OnshoreBondFilterV3DTO::getUpdateTime)
                    .max(Timestamp::compareTo).orElse(new Timestamp(0));
            lastUpdateTime = currentMaxUpdateTime;
            id = currentMaxId;
            startBondUniCode = onshoreBondFilterV3DTOs.stream()
                    .filter(x -> x.getUpdateTime().compareTo(currentMaxUpdateTime) == 0)
                    .mapToLong(OnshoreBondFilterV3DTO::getBondUniCode).max().orElse(startBondUniCode);
            fetchSize = onshoreBondFilterV3DTOs.size();
            internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(ONSHORE_BOND_FILTER_SYNC_KEY, String.valueOf(lastUpdateTime.getTime())));
            internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(ONSHORE_BOND_FILTER_SYNC_ID_KEY, String.valueOf(id)));
        }
        logger.info("[syncBondFilters] end, effectRows: {}", effectRows);
    }

    @Override
    public void syncDwdCbValuationByMq(List<CanalFlatMessageBO<DwdCbValuationMqDTO>> canalFlatMessageList) {
        Map<String, List<CanalFlatMessageBO<DwdCbValuationMqDTO>>> opType2CanalFlatMessageListMap = canalFlatMessageList.stream()
                .collect(Collectors.groupingBy(CanalFlatMessageBO::getType));
        if (opType2CanalFlatMessageListMap.containsKey(DataOperationEnum.INSERT.getText())) {
            List<CanalFlatMessageBO<DwdCbValuationMqDTO>> canalFlatMessages = opType2CanalFlatMessageListMap.get(DataOperationEnum.INSERT.getText());
            Map<Long, List<DwdCbValuationMqDTO>> dwdCbValuationMqMap = canalFlatMessages.stream().map(CanalFlatMessageBO::getData)
                    .flatMap(Collection::stream)
                    .filter(x -> Objects.nonNull(x.getValuationDate()))
                    // 过滤等于今天的 数据
                    .filter(x -> x.getValuationDate().compareTo(Date.valueOf(LocalDate.now())) == 0)
                    .collect(Collectors.groupingBy(DwdCbValuationMqDTO::getBondUniCode));
            List<OnshoreBondFilterDO> collect = dwdCbValuationMqMap.values().stream().map(this::buildCbOnshoreBondFilterDO)
                    .filter(Optional::isPresent).map(Optional::get)
                    .collect(Collectors.toList());
            onshoreBondFilterDAO.updateCbYteYtm(collect);
        }
    }

    @Override
    public void syncDwdCsValuationByMq(List<CanalFlatMessageBO<CsBondValuationMqDTO>> canalFlatMessageList) {
        LocalDate beforeTradingDay = holidayOptionalService.getBeforeTradingDay();
        Map<String, List<CanalFlatMessageBO<CsBondValuationMqDTO>>> opType2CanalFlatMessageListMap = canalFlatMessageList.stream()
                .collect(Collectors.groupingBy(CanalFlatMessageBO::getType));
        if (opType2CanalFlatMessageListMap.containsKey(DataOperationEnum.INSERT.getText())) {
            List<CanalFlatMessageBO<CsBondValuationMqDTO>> canalFlatMessages = opType2CanalFlatMessageListMap.get(DataOperationEnum.INSERT.getText());
            List<Long> bondUniCodes = canalFlatMessages.stream().map(CanalFlatMessageBO::getData)
                    .flatMap(Collection::stream)
                    .filter(x -> Objects.nonNull(x.getValuationDate()))
                    .filter(x -> Objects.equals(x.getDeleted(), Deleted.NO_DELETED.getValue()))
                    // 过滤等于昨天的 数据
                    .filter(x -> x.getValuationDate().compareTo(Date.valueOf(beforeTradingDay)) >= 0)
                    .map(CsBondValuationMqDTO::getBondUniCode).distinct().collect(Collectors.toList());
            Map<Long, List<CsBondValuationMqDTO>> dwdCbValuationMqMap1 = dwdBondValuationServiceHttpService
                    .listBondCsValuationByDateAndUniCodes(Date.valueOf(beforeTradingDay), bondUniCodes)
                    .stream()
                    .filter(x -> Objects.nonNull(x.getBondUniCode()))
                    .map(v -> {
                        CsBondValuationMqDTO csBondValuationMqDTO = new CsBondValuationMqDTO();
                        csBondValuationMqDTO.setBondUniCode(v.getBondUniCode());
                        csBondValuationMqDTO.setValuationDate(v.getValuationDate());
                        csBondValuationMqDTO.setValuationYield(v.getValuationYield());
                        csBondValuationMqDTO.setValuationType(v.getValuationType());
                        return csBondValuationMqDTO;
                    }).collect(Collectors.groupingBy(CsBondValuationMqDTO::getBondUniCode));
            List<OnshoreBondFilterDO> collect = dwdCbValuationMqMap1.values().stream().map(this::buildCsOnshoreBondFilterDO)
                    .filter(Optional::isPresent).map(Optional::get)
                    .filter(v -> Objects.nonNull(v.getBondUniCode()))
                    .collect(Collectors.toList());
            onshoreBondFilterDAO.updateCsYteYtm(collect);
        }
    }

    @Override
    public int syncMutiComUniCodeBondFilters(Long[] comUniCodes) {
        Long[] bondUniCodes = dwsPartyInfoServiceHttpService.listComBondDetailDTOs(Stream.of(comUniCodes).collect(Collectors.toList())).stream()
                .map(ComBondDetailDTO::getBondUniCode).distinct().toArray(Long[]::new);
        AtomicInteger effectRows = new AtomicInteger();
        Lists.partition(Arrays.asList(bondUniCodes), FIVE_HUNDRED).forEach(partition -> {
            effectRows.addAndGet(syncMutiBondUniCodeBondFilters(partition.toArray(new Long[0])));
        });
        return effectRows.get();
    }

    @Override
    public int syncMutiBondUniCodeBondFilters(Long... bondUniCodes) {
        logger.info("[syncSingleBondFilters] start");
        List<OnshoreBondFilterV3DTO> onshoreBondFilterV3DTOs = bondInfoService.listOnshoreBondFilterV3DTO(bondUniCodes);
        // 获取 基础map，列表形式
        if (CollectionUtils.isEmpty(onshoreBondFilterV3DTOs)) {
            return 0;
        }
        // 通过bondUniCodes 批量再次获取 基础和价格的一些字段数据
        FetchBondInfoAndPriceBO invokeResp = invokeByBondUniCodes(onshoreBondFilterV3DTOs);
        // 转换为实体
        List<OnshoreBondFilterDO> bondFilterDOs = onshoreBondFilterV3DTOs.stream()
                .map(bondFilter -> toEntity(bondFilter, invokeResp))
                .collect(Collectors.toList());
        int effectRows = onshoreBondFilterDAO.saveBondFilters(bondFilterDOs);
        logger.info("[syncSingleBondFilters] end, effectRows: {}", effectRows);
        return effectRows;
    }

    private Optional<OnshoreBondFilterDO> buildCsOnshoreBondFilterDO(List<CsBondValuationMqDTO> csBondValuations) {
        Map<Integer, CsBondValuationMqDTO> dwd = csBondValuations.stream()
                .collect(Collectors.toMap(CsBondValuationMqDTO::getValuationType, Function.identity(), (v1, v2) -> v2));
        if (MapUtils.isEmpty(dwd)) {
            return Optional.empty();
        }
        OnshoreBondFilterDO entity = new OnshoreBondFilterDO();
        Optional.ofNullable(dwd.get(ValuationTypeEnum.MATURITY.getValue())).ifPresent(x -> {
            entity.setCsYtm(x.getValuationYield());
            entity.setBondUniCode(x.getBondUniCode());
        });
        Optional.ofNullable(dwd.get(ValuationTypeEnum.EXERCISE.getValue())).ifPresent(x -> {
            entity.setCsYte(x.getValuationYield());
            entity.setBondUniCode(x.getBondUniCode());
        });
        return Optional.of(entity);
    }

    private Optional<OnshoreBondFilterDO> buildCbOnshoreBondFilterDO(List<DwdCbValuationMqDTO> dwdCbValuationMqDTOS) {
        Map<Integer, DwdCbValuationMqDTO> dwd = dwdCbValuationMqDTOS.stream()
                .collect(Collectors.toMap(DwdCbValuationMqDTO::getValuationType, Function.identity(), (v1, v2) -> v2));
        if (MapUtils.isEmpty(dwd)) {
            return Optional.empty();
        }
        OnshoreBondFilterDO entity = new OnshoreBondFilterDO();
        Optional.ofNullable(dwd.get(ValuationTypeEnum.MATURITY.getValue())).ifPresent(x -> {
            entity.setCbYtm(x.getValuationYield());
            entity.setBondUniCode(x.getBondUniCode());
        });
        Optional.ofNullable(dwd.get(ValuationTypeEnum.EXERCISE.getValue())).ifPresent(x -> {
            entity.setCbYte(x.getValuationYield());
            entity.setBondUniCode(x.getBondUniCode());
        });
        return Optional.of(entity);
    }


    @SuppressWarnings("squid:S1188")
    private OnshoreBondFilterDO toEntity(OnshoreBondFilterV3DTO bondFilterDTO, FetchBondInfoAndPriceBO invokeResp) {
        OnshoreBondFilterDO entity = BeanCopyUtils.copyProperties(bondFilterDTO, OnshoreBondFilterDO.class);
        entity.setComYyRatingV2Mapping(bondFilterDTO.getComYyRatingV2Mapping());

        // 使用 对方的 bondUniCode 作为本地的 id
        entity.setId(bondFilterDTO.getBondUniCode());
        entity.setUpdateTime(null);
        entity.setCreateTime(null);
        // 填充一些新增的属性
        Map<Long, OnshoreBondInfoDTO> bondInfoMap = invokeResp.getBondInfoMap();
        Map<Long, BondBasicDTO> bondBasicMap = invokeResp.getBondBasicMap();
        Map<Long, BondTypeDTO> bondTypeMap = invokeResp.getBondTypeDTOMap();
        Map<Long, BondOptionStatusDTO> bondOptionStatusMap = invokeResp.getBondOptionStatusDTOMap();
        Map<Long, BondIssueDTO> bondIssueMap = invokeResp.getBondIssueDTOMap();
        Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExerciseDTOMap = invokeResp.getCsBondValuationBusinessExerciseDTOMap();
        Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExpireDTOMap = invokeResp.getCsBondValuationBusinessExpireDTOMap();
        Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessOtherDTOMap = invokeResp.getCsBondValuationBusinessOtherDTOMap();
        Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExerciseDTOMap = invokeResp.getDwdCbValuationBusinessExerciseDTOMap();
        Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExpireDTOMap = invokeResp.getDwdCbValuationBusinessExpireDTOMap();
        Map<Long, List<BondGuaranteeDTO>> bondGuaranteeMap = invokeResp.getBondGuaranteeMap();
        Map<Long, BondIssueAgencyInfoDTO> bondIssueAgencyInfoMap = invokeResp.getBondIssueAgencyInfoMap();
        Map<Long, ComBondDetailDTO> comBondDetailDTOMap = invokeResp.getComBondDetailDTOMap();

        Optional.ofNullable(bondInfoMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            if (!Objects.equals(e.getExerciseDate(), "--")) {
                entity.setExerciseDate(e.getExerciseDate());
            }
            entity.setRemainingTenorDay(e.getRemainingTenorDay());
            entity.setRemainingTenor(e.getRemainingTenor());
            entity.setListDate(e.getListDate());
            entity.setComFullName(e.getComFullName());
            entity.setLatestCouponRate(e.getLatestCouponRate());
        });

        Optional.ofNullable(bondBasicMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setBondCode(e.getBondCode());
            entity.setBondShortName(e.getBondShortName());
            entity.setMaturityDate(e.getActualMaturityDate());
            entity.setRegNoticeNumber(e.getRegNoticeNumber());
            entity.setCapitalCollectionUsage(e.getProceedsUse());
        });

        Optional.ofNullable(bondTypeMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setOutstandingStatus(e.getOutstandingStatus());
            entity.setBondType(e.getCfetsBasedTypeCode());
        });

        Optional.ofNullable(bondOptionStatusMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setCouponAdjustableStatus(e.getCouponAdjustableFlag() ? 1 : 0);
            entity.setRedeemStatus(e.getCallableFlag() ? 1 : 0);
            entity.setPutOptionStatus(e.getPutableFlag() ? 1 : 0);
        });

        Optional.ofNullable(bondIssueMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setIssueStartDate(e.getIssueStartDate());
            entity.setIssuePrice(e.getIssuePrice());
        });

        Optional.ofNullable(dwdCbValuationBusinessExerciseDTOMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setCbYte(e.getValuationYield());
        });
        Optional.ofNullable(dwdCbValuationBusinessExpireDTOMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setCbYtm(e.getValuationYield());
        });
        Optional.ofNullable(csBondValuationBusinessExerciseDTOMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setCsYte(e.getValuationYield());
        });
        Optional.ofNullable(csBondValuationBusinessExpireDTOMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setCsYtm(e.getValuationYield());
        });
        Optional.ofNullable(csBondValuationBusinessOtherDTOMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setCsYtm(e.getValuationYield());
        });
        Optional.ofNullable(bondGuaranteeMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setGuarantor(e.stream().map(BondGuaranteeDTO::getGuarComFullName).filter(Objects::nonNull).collect(Collectors.joining(StringPool.COMMA)));
        });
        Optional.ofNullable(bondIssueAgencyInfoMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setLeadUnderwriter(e.getComFullName());
        });
        Optional.ofNullable(comBondDetailDTOMap).map(e -> e.get(bondFilterDTO.getBondUniCode())).ifPresent(e -> {
            entity.setBondType(e.getBondType());
        });
        return entity;
    }

    private FetchBondInfoAndPriceBO invokeByBondUniCodes(
            List<OnshoreBondFilterV3DTO> v3DTOs) {
        FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO = new FetchBondInfoAndPriceBO();
        if (CollectionUtils.isEmpty(v3DTOs)) {
            return fetchBondInfoAndPriceBO;
        }
        Set<Long> bondUniCodes = v3DTOs.stream().map(OnshoreBondFilterV3DTO::getBondUniCode).collect(Collectors.toSet());
        SwThreadPoolWorker<Void> worker = SwThreadPoolWorker.of(WORK_POOL);
        LocalDate beforeTradingDay = holidayOptionalService.getBeforeTradingDay();
        try {
            worker.addWork(() -> fetchBondInfoMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchBondBasicMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchBondTypeMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchBondOptionStatusMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchBondIssueMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchChinaBondValuationMaps(bondUniCodes, fetchBondInfoAndPriceBO, beforeTradingDay));
            worker.addWork(() -> fetchCsValuationMaps(bondUniCodes, fetchBondInfoAndPriceBO, beforeTradingDay));
            worker.addWork(() -> fetchBondGuaranteeMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchBondIssueAgencyInfoMap(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.addWork(() -> fetchComBondDetailDTOMaps(bondUniCodes, fetchBondInfoAndPriceBO));
            worker.doWorks();
        } catch (Exception e) {
            logger.error("并行执行任务时发生异常", e);
        }
        return fetchBondInfoAndPriceBO;
    }

    /**
     * 根据主体唯一编码批量获取债券的基础信息(comUniCodes不超过500)
     */
    private void fetchComBondDetailDTOMaps(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, ComBondDetailDTO> comBondDetailDTOMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> dwsPartyInfoServiceHttpService.listComBondDetailDTOs(partition))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(ComBondDetailDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setComBondDetailDTOMap(comBondDetailDTOMap);
        } catch (Exception e) {
            logger.error("获取债券基础信息失败", e);
        }
    }


    /**
     * 券发行的发行中介信息
     */
    private void fetchBondIssueAgencyInfoMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, BondIssueAgencyInfoDTO> bondGuaranteeMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> dwsBondInfoServiceHttpService.listBondIssueAgencyInfoDTOs(partition))
                    .flatMap(Collection::stream)
                    .filter(x -> Objects.equals(x.getIssueNum(), BigDecimal.ZERO.intValue()))
                    .filter(x -> Objects.equals(x.getIssueAgencyTypeCode(), IssueAgencyTypeEnum.DEPUTY_LEAD_UNDERWRITER.getValue())
                            || Objects.equals(x.getIssueAgencyTypeCode(), IssueAgencyTypeEnum.BOOK_MANAGER.getValue()))
                    .collect(Collectors.toMap(BondIssueAgencyInfoDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setBondIssueAgencyInfoMap(bondGuaranteeMap);
        } catch (Exception e) {
            logger.error("获取债券基础信息失败", e);
        }
    }


    /**
     * 境内债担保信息
     */
    private void fetchBondGuaranteeMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, List<BondGuaranteeDTO>> bondGuaranteeMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> dwsBondInfoServiceHttpService.listBondGuaranteeDTOs(partition))
                    .flatMap(Collection::stream)
                    .collect(Collectors.groupingBy(BondGuaranteeDTO::getBondUniCode));
            fetchBondInfoAndPriceBO.setBondGuaranteeMap(bondGuaranteeMap);
        } catch (Exception e) {
            logger.error("获取债券基础信息失败", e);
        }
    }

    /**
     * 获取债券基础信息
     */
    private void fetchBondInfoMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, OnshoreBondInfoDTO> bondInfoDTOMap = Maps.newHashMap();
            List<OnshoreBondInfoDTO> bondInfoList = bondInfoService.listBondInfos(bondUniCodes);
            Optional.ofNullable(bondInfoList)
                    .ifPresent(b -> bondInfoDTOMap.putAll(
                            b.stream().collect(Collectors.toMap(OnshoreBondInfoDTO::getBondUniCode, x -> x))));
            fetchBondInfoAndPriceBO.setBondInfoMap(bondInfoDTOMap);
        } catch (Exception e) {
            logger.error("获取债券基础信息失败", e);
        }
    }

    /**
     * 获取债券基础接口数据
     */
    private void fetchBondBasicMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, BondBasicDTO> bondBasicDTOMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> dwsBondInfoServiceHttpService.listOnshoreBondBasicInfo(partition.toArray(new Long[0])))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(BondBasicDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setBondBasicMap(bondBasicDTOMap);
        } catch (Exception e) {
            logger.error("获取债券基础接口数据失败", e);
        }
    }

    /**
     * 获取债券类型信息
     */
    private void fetchBondTypeMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, BondTypeDTO> bondTypeDTOMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> dwsBondInfoServiceHttpService.getBondTypeByBondUniCodes(partition))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(BondTypeDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setBondTypeDTOMap(bondTypeDTOMap);
        } catch (Exception e) {
            logger.error("获取债券类型信息失败", e);
        }
    }

    /**
     * 获取债券含权状态信息
     */
    private void fetchBondOptionStatusMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, BondOptionStatusDTO> bondOptionStatusDTOMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> {
                        BondOptionRequestDTO requestDTO = new BondOptionRequestDTO();
                        requestDTO.setBondUniCodes(partition);
                        return dwsBondInfoServiceHttpService.listBondOptionStatusByBondUniCodes(requestDTO);
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toMap(BondOptionStatusDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setBondOptionStatusDTOMap(bondOptionStatusDTOMap);
        } catch (Exception e) {
            logger.error("获取债券含权状态信息失败", e);
        }
    }

    /**
     * 获取债券发行信息
     */
    private void fetchBondIssueMap(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO) {
        try {
            Map<Long, BondIssueDTO> bondIssueDTOMap = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> {
                        BondIssueRequestDTO requestDTO = new BondIssueRequestDTO();
                        requestDTO.setBondUniCodes(partition);
                        // 默认传0
                        requestDTO.setIssueNum(BigDecimal.ZERO.intValue());
                        return dwsBondInfoServiceHttpService.listBondIssueByBondUniCodes(requestDTO);
                    }).flatMap(Collection::stream)
                    .collect(Collectors.toMap(BondIssueDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setBondIssueDTOMap(bondIssueDTOMap);
        } catch (Exception e) {
            logger.error("获取债券发行信息失败", e);
        }
    }

    /**
     * 获取中债估值数据
     */
    private void fetchChinaBondValuationMaps(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO, LocalDate beforeTradingDay) {
        try {
            List<DwdCbValuationDTO> dwdCbValuationDTOS = Lists.partition(Lists.newArrayList(bondUniCodes), TWO_HUNDRED).stream()
                    .map(partition -> onshoreDwdChinaBondHttpService.listDwdCbValuationDTOs(
                            // 六点半之前前一天，六点半之后当天
                            partition, LocalDateTime.now().isBefore(LocalDateTime.of(LocalDate.now(), CHINA_BOND_START_TIME))
                                    ? Date.valueOf(beforeTradingDay)
                                    : Date.valueOf(LocalDate.now())))
                    .flatMap(Collection::stream).collect(Collectors.toList());

            // 到期z
            Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExpireDTOMap = dwdCbValuationDTOS.stream()
                    .filter(v -> Objects.equals(v.getValuationType(), ValuationTypeEnum.MATURITY.getValue()))
                    .collect(Collectors.toMap(DwdCbValuationDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            // 行权
            Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExerciseDTOMap = dwdCbValuationDTOS.stream()
                    .filter(v -> Objects.equals(v.getValuationType(), ValuationTypeEnum.EXERCISE.getValue()))
                    .collect(Collectors.toMap(DwdCbValuationDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            fetchBondInfoAndPriceBO.setDwdCbValuationBusinessExpireDTOMap(dwdCbValuationBusinessExpireDTOMap);
            fetchBondInfoAndPriceBO.setDwdCbValuationBusinessExerciseDTOMap(dwdCbValuationBusinessExerciseDTOMap);
        } catch (Exception e) {
            logger.error("获取中债估值数据失败", e);
        }
    }

    /**
     * 获取中证估值数据
     */
    private void fetchCsValuationMaps(Set<Long> bondUniCodes, FetchBondInfoAndPriceBO fetchBondInfoAndPriceBO, LocalDate beforeTradingDay) {
        try {
            List<CsBondValuationBusinessDTO> csBondValuationBusinessDTOS = Lists.partition(Lists.newArrayList(bondUniCodes), FIVE_HUNDRED).stream()
                    .map(partition -> dwdBondValuationServiceHttpService.listBondCsValuationByDateAndUniCodes(
                            Date.valueOf(beforeTradingDay), partition))
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            // 到期
            Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExpireDTOMap = csBondValuationBusinessDTOS.stream()
                    .filter(v -> Objects.equals(v.getValuationType(), ValuationTypeEnum.MATURITY.getValue()))
                    .collect(Collectors.toMap(CsBondValuationBusinessDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            // 行权
            Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExerciseDTOMap = csBondValuationBusinessDTOS.stream()
                    .filter(v -> Objects.equals(v.getValuationType(), ValuationTypeEnum.EXERCISE.getValue()))
                    .collect(Collectors.toMap(CsBondValuationBusinessDTO::getBondUniCode, x -> x, (v1, v2) -> v2));
            // 其他
            Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessOtherDTOMap = csBondValuationBusinessDTOS.stream()
                    .filter(v ->
                            !Objects.equals(v.getValuationType(), ValuationTypeEnum.MATURITY.getValue())
                                    && !Objects.equals(v.getValuationType(), ValuationTypeEnum.EXERCISE.getValue()))
                    .collect(Collectors.toMap(CsBondValuationBusinessDTO::getBondUniCode, x -> x, (v1, v2) -> v2));

            fetchBondInfoAndPriceBO.setCsBondValuationBusinessExerciseDTOMap(csBondValuationBusinessExerciseDTOMap);
            fetchBondInfoAndPriceBO.setCsBondValuationBusinessExpireDTOMap(csBondValuationBusinessExpireDTOMap);
            fetchBondInfoAndPriceBO.setCsBondValuationBusinessOtherDTOMap(csBondValuationBusinessOtherDTOMap);
        } catch (Exception e) {
            logger.error("获取中证估值数据失败", e);
        }
    }

    private Optional<String> toGuarFullNameStr(Collection<String> guarFullNames) {
        if (CollectionUtils.isEmpty(guarFullNames)) {
            return Optional.empty();
        }
        StringJoiner joiner = new StringJoiner(GUAR_FULL_NAME_CONNECTOR);
        for (String guarFullName : guarFullNames) {
            joiner.add(guarFullName);
        }
        return Optional.of(joiner.toString());
    }

}
