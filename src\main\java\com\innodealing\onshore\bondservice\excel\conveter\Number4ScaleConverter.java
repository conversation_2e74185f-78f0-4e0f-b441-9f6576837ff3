package com.innodealing.onshore.bondservice.excel.conveter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Objects;

/**
 * 保留两位小数 转换器
 *
 * <AUTHOR>
 * @date 2023/02/21
 */
public class Number4ScaleConverter implements Converter<Number> {
    @Override
    public WriteCellData<?> convertToExcelData(Number value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws ParseException {
        if (Objects.isNull(value)) {
            return null;
        }
        DecimalFormat df = new DecimalFormat("##,##0.00##");
        df.setRoundingMode(RoundingMode.HALF_UP);
        final String format = df.format(value);
        return new WriteCellData<>(format);
    }
}
