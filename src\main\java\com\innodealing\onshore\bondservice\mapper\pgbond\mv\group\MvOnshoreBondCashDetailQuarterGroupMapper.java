package com.innodealing.onshore.bondservice.mapper.pgbond.mv.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailQuarterGroupDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.mv.MvOnshoreBondCashDetailQuarterDO;

/**
 * 国内债券现金流按季度统计物化视图
 *
 * <AUTHOR>
 */
public interface MvOnshoreBondCashDetailQuarterGroupMapper extends SelectByGroupedQueryMapper<MvOnshoreBondCashDetailQuarterDO, MvOnshoreBondCashDetailQuarterGroupDO> {
}