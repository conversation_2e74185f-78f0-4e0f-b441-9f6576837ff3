package com.innodealing.onshore.bondservice.config.constant;

import java.math.BigDecimal;

/**
 * 数字类型常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2025年05月08日 13:47:00
 */
public class NumberConstant {

    private NumberConstant() {
    }

    /**
     * 数字 int 2
     */
    public static final Integer INT_TWO = 2;
    /**
     * 数字 int 3
     */
    public static final Integer INT_THREE = 3;

    /**
     * 数字 int 5
     */
    public static final Integer INT_FIVE = 5;

    /**
     * 数字 int 8
     */
    public static final Integer INT_EIGHT = 8;
    /**
     * 数字 int 10
     */
    public static final Integer INT_TEN = 10;
    /**
     * 批量大小
     */
    public static final Integer TWO_HUNDRED = 200;
    /**
     * 批量大小
     */
    public static final Integer FIVE_HUNDRED = 500;

    /**
     *1000
     */
    public static  final  Long ONE_THOUSAND=1000L;

    /**
     *10000
     */
    public static  final  Long TEN_THOUSAND=10000L;

    /**
     * 100
     */
    public static final BigDecimal HUNDRED =  new BigDecimal(100L);
}
