package com.innodealing.onshore.bondservice.model.dto;

import io.swagger.annotations.ApiModelProperty;


/**
 * abs 债券主体基础信息返回
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2025年06月12日 15:48:00
 */
public class AbsBondShortInfoDTO {

    @ApiModelProperty("债券代码")
    private Long bondUniCode;
    /**
     * 债券全称
     */
    @ApiModelProperty("债券全称")
    private String bondFullName;
    /**
     * 债券简称
     */
    @ApiModelProperty("债券简称")
    private String bondShortName;

    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public String getBondFullName() {
        return bondFullName;
    }

    public void setBondFullName(String bondFullName) {
        this.bondFullName = bondFullName;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }
}
