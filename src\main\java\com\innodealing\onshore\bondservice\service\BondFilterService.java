package com.innodealing.onshore.bondservice.service;


import com.innodealing.onshore.bondmetadata.bo.rocketmq.CanalFlatMessageBO;
import com.innodealing.onshore.bondservice.model.dto.CsBondValuationMqDTO;
import com.innodealing.onshore.bondservice.model.dto.DwdCbValuationMqDTO;

import javax.annotation.Nullable;
import java.util.List;

/**
 * 债券筛选服务
 *
 * <AUTHOR>
 */
public interface BondFilterService {

    /**
     * 同步单个 filter 数据
     *
     * @param bondUniCodes bondUniCodes
     * @return do
     */
    int syncMutiBondUniCodeBondFilters(Long... bondUniCodes);

    /**
     * 同步债券筛选
     *
     * @param lastTimeStamp 上次同步时间
     * @param deleted       是否删除不存在的债券 1:删除 0:不删除
     * @return 影响行数
     */
    void syncBondFilters(@Nullable Long lastTimeStamp, @Nullable Integer deleted);

    /**
     * 同步DWD_CHINA_BOND_DWD_CB_VALUATION数据
     * @param canalFlatMessageList canal flat message list
     */
    void syncDwdCbValuationByMq(List<CanalFlatMessageBO<DwdCbValuationMqDTO>> canalFlatMessageList);

    /**
     * 同步DWD_CS_BOND_VALUATION数据
     * @param canalFlatMessageList canal flat message list
     */
    void syncDwdCsValuationByMq(List<CanalFlatMessageBO<CsBondValuationMqDTO>> canalFlatMessageList);

    /**
     * 同步主体信息
     * @param comUniCodes comUniCodes
     * @return 影响行数
     */
    int syncMutiComUniCodeBondFilters(Long[] comUniCodes);
}
