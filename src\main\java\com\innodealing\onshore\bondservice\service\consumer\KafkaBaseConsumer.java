package com.innodealing.onshore.bondservice.service.consumer;

import org.apache.kafka.clients.consumer.ConsumerRecord;

/**
 * kafka 消费基类
 *
 * <AUTHOR>
 * @date 2024/8/22 15:39
 */
public interface KafkaBaseConsumer {

    /**
     * 生成 messageId
     *
     * @param record 消息
     * @return messageId
     */
    default String getKafkaMessageId(ConsumerRecord<String, String> record) {
        String topic = record.topic();
        int partition = record.partition();
        long offset = record.offset();
        return topic + "-" + partition + "-" + offset;
    }
}
