package com.innodealing.onshore.bondservice.service.consumer;

import com.innodealing.onshore.service.CacheOptionalService;

import javax.annotation.Resource;

import static com.innodealing.onshore.bondservice.config.constant.RedisKeyConstant.REDIS_KEY_MQ_MESSAGE_SKIP_STATUS;


/**
 * 队列 基类
 *
 * <AUTHOR>
 * @date 2024/10/23 11:22
 */
public class MqBaseConsumer implements KafkaBaseConsumer{
    @Resource
    protected CacheOptionalService cacheOptionalService;

    /**
     * 获取 mq消息 过滤状态
     *
     * @return 1开启消息过滤  0 不过滤消息
     */
    public Boolean getMqSkipStatus() {
        String key = cacheOptionalService.getKey(REDIS_KEY_MQ_MESSAGE_SKIP_STATUS, this.getClass().getSimpleName());
        return cacheOptionalService.get(key, Integer.class).orElse(0) == 1;
    }
}
