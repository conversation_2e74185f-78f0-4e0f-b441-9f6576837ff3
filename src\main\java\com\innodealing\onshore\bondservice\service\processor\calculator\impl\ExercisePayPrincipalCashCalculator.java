package com.innodealing.onshore.bondservice.service.processor.calculator.impl;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.utils.BigDecimalUtils;
import com.innodealing.onshore.bondservice.config.constant.NumberConstant;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.service.processor.calculator.AbstractBondCashDetailCalculator;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import com.innodealing.onshore.bondservice.utils.BondCashCalculationUtils;
import org.springframework.stereotype.Component;

import java.math.RoundingMode;
import java.sql.Date;
import java.util.Collections;
import java.util.Comparator;
import java.util.Objects;
import java.util.Optional;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.HUNDRED;

/**
 * 行权偿付本金 计算器
 * 负责计算行权偿付本金字段
 *
 * <AUTHOR>
 */
@Component
public class ExercisePayPrincipalCashCalculator extends AbstractBondCashDetailCalculator {

    @Override
    public String getName() {
        return ExercisePayPrincipalCashCalculator.class.getName();
    }

    @Override
    public int getPriority() {
        return NumberConstant.INT_EIGHT;
    }

    @Override
    protected void doCalculate(BondProcessContext context, Long bondUniCode) {
        // 使用工具类进行数据验证
        if (!BondCashCalculationUtils.validateContextData(context, bondUniCode, ExercisePayPrincipalCashCalculator.class.getName())) {
            return;
        }

        BondOptionStatusDTO bondOptionStatusDTO = context.getBondOptionStatusMap().getOrDefault(bondUniCode, new BondOptionStatusDTO());
        // 含权
        if (this.isEmbeddedOptionBond(bondOptionStatusDTO)) {
            handleContainRightBond(context, bondUniCode);
        } else {
            handleNotContainRightBond(context, bondUniCode);
        }
    }

    /**
     * 处理不含权债券以及非回售赎回债券
     */
    private void handleNotContainRightBond(BondProcessContext context, Long bondUniCode) {
        // 使用工具类和父类常量处理不含权债券本金计算
        BondCashCalculationUtils.processNonRightBondPrincipal(
                context,
                bondUniCode,
                BondCashCalculationUtils.groupBondAmountsByKey(
                        context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList())
                                .stream()
                                .filter(bondAmount -> FILTER_CHANGE_REASONS.contains(bondAmount.getChangeReasonCode()))
                                .collect(java.util.stream.Collectors.toList()),
                        this::getBondAmountKey
                ),
                OnshoreBondCashDetailBO::setExercisePayPrincipalCash
        );
    }

    /**
     * 处理含权债券且回售赎回
     */
    private void handleContainRightBond(BondProcessContext context, Long bondUniCode) {
        // 处理历史数据
        BondCashCalculationUtils.processRightBondHistoricalPrincipal(
                context,
                bondUniCode,
                context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList()),
                FILTER_CHANGE_REASONS,
                this::getBondAmountKey,
                OnshoreBondCashDetailBO::setExercisePayPrincipalCash
        );

        // 处理未来数据
        Optional<BondCashFlowViewDTO> filterBondCashFlowOpt = filterExerciseBondCashFlow(context, bondUniCode);
        if (!filterBondCashFlowOpt.isPresent()) {
            logger.warn("ExercisePayPrincipalCashCalculator#handleContainRightBond 现金流不存在,跳过：com_uni_code:{},bond_uni_code:{}",
                    context.getComUniCode(), bondUniCode);
            return;
        }
        // 结息日
        Date date = filterBondCashFlowOpt.get().getInterestEndDate();
        OnshoreBondInfoDTO onshoreBondInfoDTO = context.getOnshoreBondInfoDTOMap().get(bondUniCode);
        if (Objects.isNull(onshoreBondInfoDTO) || Objects.isNull(date)) {
            logger.warn("ExercisePayPrincipalCashCalculator#handleContainRightBond 获取债券信息失败,跳过：com_uni_code:{},bond_uni_code:{},date:{}",
                    context.getComUniCode(), bondUniCode, date);
            return;
        }
        BondAmountDTO bondAmountDto = context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList()).stream()
                .filter(v -> Objects.nonNull(v.getChangeDate()))
                .filter(v -> date.toLocalDate().isAfter(v.getChangeDate().toLocalDate()))
                .min(Comparator.comparing(BondAmountDTO::getRemainAmount))
                .orElseGet(BondAmountDTO::new);


        // 结息日+1天
        OnshoreBondCashDetailBO cashDetail = context.getOrCreateCashDetail(bondUniCode, Date.valueOf(date.toLocalDate().plusDays(1)));
        // 偿付本金计算：行权本金支付 * 剩余规模 / 100
        BigDecimalUtils.safeMultiply(filterBondCashFlowOpt.get().getOptionPrincipalPayment(), bondAmountDto.getRemainAmount())
                .flatMap(v -> BigDecimalUtils.safeDivide(v, HUNDRED, RoundingMode.HALF_UP))
                .ifPresent(cashDetail::setExercisePayPrincipalCash);
    }


}
