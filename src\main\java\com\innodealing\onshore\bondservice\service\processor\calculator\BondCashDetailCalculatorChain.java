package com.innodealing.onshore.bondservice.service.processor.calculator;

import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

/**
 * 现金流明细计算器链
 * 使用责任链模式组织多个计算器
 *
 * <AUTHOR>
 */
@Component
public class BondCashDetailCalculatorChain {

    private static final Logger logger = LoggerFactory.getLogger(BondCashDetailCalculatorChain.class);

    /**
     * 计算器列表，按优先级排序
     */
    private final List<BondCashDetailCalculator> calculators;

    /**
     * 构造函数
     */
    public BondCashDetailCalculatorChain() {
        this.calculators = new ArrayList<>();
    }

    /**
     * 添加计算器
     *
     * @param calculator 计算器
     */
    public void addCalculator(BondCashDetailCalculator calculator) {
        if (calculator != null) {
            calculators.add(calculator);
            // 按优先级排序
            calculators.sort(Comparator.comparingInt(BondCashDetailCalculator::getPriority));
            logger.info("Added calculator: {} with priority: {}", calculator.getName(), calculator.getPriority());
        }
    }

    /**
     * 移除计算器
     *
     * @param calculatorName 计算器名称
     * @return 当前链对象，支持链式调用
     */
    public BondCashDetailCalculatorChain removeCalculator(String calculatorName) {
        calculators.removeIf(calculator -> calculator.getName().equals(calculatorName));
        logger.info("Removed calculator: {}", calculatorName);
        return this;
    }

    /**
     * 执行计算链
     *
     * @param context     处理上下文
     * @param bondUniCode 债券唯一编码
     */
    public void calculate(BondProcessContext context, Long bondUniCode) {
        if (calculators.isEmpty()) {
            logger.warn("No calculators configured in the chain");
            return;
        }
        for (BondCashDetailCalculator calculator : calculators) {
            try {
                calculator.calculate(context, bondUniCode);
            } catch (Exception e) {
                logger.error("Error executing calculator: {},comUniCode:{} for bondUniCode: {}", calculator.getName(), context.getComUniCode(), bondUniCode, e);
            }
        }
    }

    /**
     * 获取所有计算器
     *
     * @return 计算器列表的副本
     */
    public List<BondCashDetailCalculator> getCalculators() {
        return new ArrayList<>(calculators);
    }

    /**
     * 获取计算器数量
     *
     * @return 计算器数量
     */
    public int size() {
        return calculators.size();
    }

    /**
     * 检查是否包含指定名称的计算器
     *
     * @param calculatorName 计算器名称
     * @return true if 包含
     */
    public boolean containsCalculator(String calculatorName) {
        return calculators.stream()
                .anyMatch(calculator -> calculator.getName().equals(calculatorName));
    }

    /**
     * 根据名称获取计算器
     *
     * @param calculatorName 计算器名称
     * @return 计算器对象，如果不存在则返回null
     */
    public BondCashDetailCalculator getCalculator(String calculatorName) {
        return calculators.stream()
                .filter(calculator -> calculator.getName().equals(calculatorName))
                .findFirst()
                .orElse(null);
    }

    /**
     * 清空所有计算器
     */
    public void clear() {
        calculators.clear();
        logger.info("Cleared all calculators from the chain");
    }

    /**
     * 获取计算器信息
     *
     * @return 计算器信息字符串
     */
    public String getCalculatorInfo() {
        if (calculators.isEmpty()) {
            return "No calculators configured";
        }

        StringBuilder info = new StringBuilder();
        info.append("Configured calculators (").append(calculators.size()).append("):\n");

        for (int i = 0; i < calculators.size(); i++) {
            BondCashDetailCalculator calculator = calculators.get(i);
            info.append(String.format("  %d. %s (priority: %d)\n",
                    i + 1, calculator.getName(), calculator.getPriority()));
        }

        return info.toString();
    }
}
