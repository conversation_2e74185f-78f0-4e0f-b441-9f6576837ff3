package com.innodealing.onshore.bondservice.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 中证债券估值返回dto
 *
 * <AUTHOR>
 * @date 2025-02-13
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CsBondValuationMqDTO {

    /**
     * DM债券唯一编码
     */
    private Long bondUniCode;
    /**
     * 估值日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date valuationDate;
    /**
     * 估价收益率，单位：%
     */
    private BigDecimal valuationYield;
    /**
     * 估值类型：1.到期，2.行权，0.其他
     */
    private Integer valuationType;

    /**
     * 是否删除，0：未删除; 1：已删除
     */
    private Integer deleted;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Date getValuationDate() {
        return valuationDate;
    }

    public void setValuationDate(Date valuationDate) {
        this.valuationDate = valuationDate;
    }

    public BigDecimal getValuationYield() {
        return valuationYield;
    }

    public void setValuationYield(BigDecimal valuationYield) {
        this.valuationYield = valuationYield;
    }

    public Integer getValuationType() {
        return valuationType;
    }

    public void setValuationType(Integer valuationType) {
        this.valuationType = valuationType;
    }

    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }
}
