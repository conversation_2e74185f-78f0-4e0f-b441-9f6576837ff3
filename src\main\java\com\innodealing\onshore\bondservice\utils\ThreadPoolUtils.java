package com.innodealing.onshore.bondservice.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;

/**
 * ThreadPoolUtil
 * 
 * <AUTHOR>
 *
 */
public class ThreadPoolUtils {

	private static final Logger LOG = LoggerFactory.getLogger(ThreadPoolUtils.class);

	/**
	 * unit： 线程池维护线程所允许的空闲时间的单位 workQueue： 线程池所使用的缓冲队列 handler： 线程池对拒绝任务的处理策略
	 * 
	 * @param corePoolSize
	 *            线程池维护线程的最少数量
	 * @param maximumPoolSize
	 *            线程池维护线程的最大数量
	 * @return
	 */
	public static ExecutorService getFixedThreadPool(int corePoolSize, int maximumPoolSize) {
		BlockingQueue<Runnable> queue = new LinkedBlockingQueue<Runnable>();
		// 拒绝策略2：用于被拒绝任务的处理程序，它直接在 execute 方法的调用线程中运行被拒绝的任务；如果执行程序已关闭，则会丢弃该任务
		RejectedExecutionHandler handler = new CallerRunsPolicy();
		// keepAliveTime 线程池维护线程所允许的空闲时间
		return new ThreadPoolExecutor(corePoolSize, maximumPoolSize, 0L, TimeUnit.SECONDS, queue, handler);
	}

	public static void shutdown(ExecutorService pool) {
		pool.shutdown();
		try {
			pool.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
		} catch (InterruptedException e) {
			LOG.error("等待任务完成中发生异常 ", e);
			e.printStackTrace();
		}
	}
	
    /**
     * A handler for rejected tasks that runs the rejected task
     * directly in the calling thread of the {@code execute} method,
     * unless the executor has been shut down, in which case the task
     * is discarded.
     */
    public static class CallerRunsPolicy implements RejectedExecutionHandler {
        /**
         * Creates a {@code CallerRunsPolicy}.
         */
        public CallerRunsPolicy() { }

        /**
         * Executes task r in the caller's thread, unless the executor
         * has been shut down, in which case the task is discarded.
         *
         * @param r the runnable task requested to be executed
         * @param e the executor attempting to execute this task
         */
        public void rejectedExecution(Runnable r, ThreadPoolExecutor e) {
        	try{
	            if (!e.isShutdown()) {
	                r.run();
	            }
        	}catch(Exception exception){
        		LOG.error("ThreadPoolUtils.CallerRunsPolicy is error", e);
        	}
        }
    }

}
