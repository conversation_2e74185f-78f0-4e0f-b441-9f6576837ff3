package com.innodealing.onshore.bondservice.controller.test;

import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.commons.http.RestResponse;
import com.innodealing.commons.json.JSON;
import com.innodealing.onshore.bondservice.model.dto.request.*;
import com.innodealing.onshore.bondservice.model.dto.response.*;
import com.innodealing.onshore.bondservice.service.BondsInCirculationService;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 流通中债券重构-api
 *
 * <AUTHOR>
 */
@Api(tags = "(internal)重构流通中债券(测试api)")
@RestController
@RequestMapping("/internal/bond/circulation")
public class TestBondsInCirculationController {

    @Resource
    private BondsInCirculationService bondsInCirculationService;


    @ApiOperation("获取主体否是民企,相同实控人,abs信息,债券类型")
    @GetMapping(value = "/com/infos")
    public RestResponse<ComDistributionDetailResponseDTO> getComDistributionDetailResponseDTO(@RequestParam Long comUniCode) {
        return RestResponse.Success(bondsInCirculationService.getComDistributionDetailResponseDTO(comUniCode));
    }


    @ApiOperation("规模分布柱状图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondCashDistributionHistogramRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/histogram", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<BondCashDistributionHistogramResponseDTO> bondCashDistributionHistogram(@RequestBody String requestBody) {
        BondCashDistributionHistogramRequestDTO requestDTO = JSON.parseObject(requestBody, BondCashDistributionHistogramRequestDTO.class);
        return RestResponse.Success(bondsInCirculationService.bondCashDistributionHistogram(requestDTO));
    }


    @ApiOperation("偿还流水分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondCashDistributionDetailPageRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/detail/paging", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<NormPagingResult<BondCashDistributionDetailPageResponseDTO>> getBondCashDistributionDetailPaging(@RequestBody String requestBody) {
        BondCashDistributionDetailPageRequestDTO requestDTO = JSON.parseObject(requestBody, BondCashDistributionDetailPageRequestDTO.class);
        return RestResponse.Success(bondsInCirculationService.getBondCashDistributionDetailPaging(requestDTO));
    }


    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondCashDistributionDetailPageRequestDTO", paramType = "body")
    })
    @ApiOperation(value = "导出偿还流水")
    @PostMapping(value = "/export", consumes = MediaType.TEXT_PLAIN_VALUE)
    public void exportBondCashDistributionDetail(@RequestBody String requestBody,
                                                 HttpServletResponse response) throws IOException {
        BondCashDistributionDetailPageRequestDTO requestDTO = JSON.parseObject(requestBody, BondCashDistributionDetailPageRequestDTO.class);
        bondsInCirculationService.exportBondCashDistributionDetail(requestDTO, response);
    }

    @ApiOperation("债券存量占比规模饼图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondCashDistributionPieChartRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/pie/charts", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<List<BondCashDistributionPieChartResponseDTO>> bondCashDistributionPieCharts(@RequestBody String requestBody) {
        BondCashDistributionPieChartRequestDTO requestDTO = JSON.parseObject(requestBody, BondCashDistributionPieChartRequestDTO.class);
        return RestResponse.Success(bondsInCirculationService.bondCashDistributionPieCharts(requestDTO));
    }


    @ApiOperation("债券明细分页")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondDetailInfoPageRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/bond/detail/page", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<NormPagingResult<BondDetailInfoPageResponseDTO>> getBondDetailInfoPaging(
            @RequestBody String requestBody,
            @ApiParam(name = "userId", value = "用户id(header)") @CookieValue("userid") Long userId) {
        BondDetailInfoPageRequestDTO requestDTO = JSON.parseObject(requestBody, BondDetailInfoPageRequestDTO.class);
        return RestResponse.Success(bondsInCirculationService.getBondDetailInfoPaging(requestDTO, userId));
    }

    @ApiOperation("债券明细统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondBalanceStatisticsRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/bond/balance/statistics", consumes = MediaType.TEXT_PLAIN_VALUE)
    public RestResponse<List<BondBalanceStatisticsResponseDTO>> listBondBalanceStatistics(@RequestBody String requestBody) {
        BondBalanceStatisticsRequestDTO requestDTO = JSON.parseObject(requestBody, BondBalanceStatisticsRequestDTO.class);
        return RestResponse.Success(bondsInCirculationService.listBondBalanceStatistics(requestDTO));
    }


    @ApiOperation("债券明细分页导出")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondDetailInfoPageExportRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/bond/detail/export", consumes = MediaType.TEXT_PLAIN_VALUE)
    public void exportBondDetailInfo(
            @RequestBody String requestBody,
            @ApiParam(name = "userId", value = "用户id(header)") @CookieValue("userid") Long userId,
            HttpServletResponse response) throws IOException {
        BondDetailInfoPageExportRequestDTO requestDTO = JSON.parseObject(requestBody, BondDetailInfoPageExportRequestDTO.class);
        bondsInCirculationService.exportBondDetailInfo(requestDTO, userId, response);
    }


}
