package com.innodealing.onshore.bondservice.model.dto.response;

import com.alibaba.excel.annotation.ExcelProperty;
import com.innodealing.onshore.bondservice.excel.conveter.AbsTenThousandToYuan2ScaleConverter;
import com.innodealing.onshore.bondservice.excel.conveter.DateDateFormat10Converter;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 流通中债券-规模分布明细分页响应参数
 *
 * <AUTHOR>
 */
public class BondCashDistributionDetaiExportResponseDTO {
    @ApiModelProperty("还本/付息日期")
    @ExcelProperty(value = "还本/付息日期", converter = DateDateFormat10Converter.class)
    private Date theoryInterestDate;
    @ApiModelProperty("债券代码")
    @ExcelProperty(value = "债券代码")
    private String bondCode;
    @ApiModelProperty("债券简称")
    @ExcelProperty(value = "债券简称")
    private String bondShortName;
    @ApiModelProperty("偿付利息(元)")
    @ExcelProperty(value = "偿付利息(元)", converter = AbsTenThousandToYuan2ScaleConverter.class)
    private BigDecimal payInterestCash;
    @ApiModelProperty("偿付本金(元)")
    @ExcelProperty(value = "偿付本金(元)", converter = AbsTenThousandToYuan2ScaleConverter.class)
    private BigDecimal payPrincipalCash;


    public BigDecimal getPayInterestCash() {
        return payInterestCash;
    }

    public void setPayInterestCash(BigDecimal payInterestCash) {
        this.payInterestCash = payInterestCash;
    }

    public BigDecimal getPayPrincipalCash() {
        return payPrincipalCash;
    }

    public void setPayPrincipalCash(BigDecimal payPrincipalCash) {
        this.payPrincipalCash = payPrincipalCash;
    }

    public Date getTheoryInterestDate() {
        return theoryInterestDate;
    }

    public void setTheoryInterestDate(Date theoryInterestDate) {
        this.theoryInterestDate = theoryInterestDate;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }
}

