package com.innodealing.onshore.bondservice.excel.handler;

import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.context.CellWriteHandlerContext;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.property.ExcelWriteHeadProperty;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 国内自定义列顺序导出模板
 *
 * <AUTHOR>
 * @description 自定义列顺序导出模板
 */
@SuppressWarnings("all")
public class DynamicColumnSortingTemplateWriteHandler implements CellWriteHandler {

    private static final String LOGO_URL = "https://dmdata-shanghai.oss-cn-shanghai.aliyuncs.com/public/bond-web/lQLPJxa0yq-kWPhdzMiwQFg9-II1ZB0DKUaHDgAQAA_200_93.png";
    private static final int CELL_WEIGHT = 12;
    private static final int HEAD_CELL_HEIGHT = 35;
    private static final int CELL_HEIGHT = 18;
    private static final int HEAD_COLOR = 30;
    private static final int HEAD_TITLE_COLOR = 22;
    private static final int HEAD_TITLE_FONT_SIZE = 14;
    private Map<Integer, Head> indexHeadMap;
    private Map<String, Integer> fieldToIndexMap;
    private String title;
    private Boolean dynamic;
    private static final int TWO = 2;
    private static final double CONVERSION = 1.333;
    private static final int UNIT_OFFSET = 9525;
    private static final int WIDTH = 256;
    private final Logger logger = LoggerFactory.getLogger(DynamicColumnSortingTemplateWriteHandler.class);


    public DynamicColumnSortingTemplateWriteHandler(String title, Map<String, Integer> fieldToIndexMap) {
        // 0是固定excel文件的不让转移，但目标字段转移可以带动0转移，例如：3，b），（0，a--》3，a），（0，b，可以将0字段放到3即传：a，3
        this.fieldToIndexMap = fieldToIndexMap.entrySet().stream().filter(x -> !Objects.equals(x.getValue(), 0))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        this.title = title;
    }


    public DynamicColumnSortingTemplateWriteHandler(String title, Map<String, Integer> fieldToIndexMap, Boolean dynamic) {
        // 0是固定excel文件的不让转移，但目标字段转移可以带动0转移，例如：3，b），（0，a--》3，a），（0，b，可以将0字段放到3即传：a，3
        this.fieldToIndexMap = fieldToIndexMap.entrySet().stream().filter(x -> !Objects.equals(x.getValue(), 0))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        this.title = title;
        this.dynamic = dynamic;
    }

    @Override
    public void beforeCellCreate(CellWriteHandlerContext context) {
        WriteSheetHolder writeSheetHolder = context.getWriteSheetHolder();
        ExcelWriteHeadProperty excelWriteHeadProperty = writeSheetHolder.excelWriteHeadProperty();
        if (Objects.isNull(this.indexHeadMap) || this.indexHeadMap.isEmpty()) {
            Set<Map.Entry<Integer, Head>> headEntrySet = excelWriteHeadProperty.getHeadMap().entrySet();
            Map<Integer, Head> headMap = headEntrySet.stream()
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            Map<Integer, Head> result = Maps.newHashMap();
            for (Map.Entry<Integer, Head> headEntry : headMap.entrySet()) {
                Head head = headEntry.getValue();
                if (Boolean.TRUE.equals(context.getHead()) && context.getRelativeRowIndex() == 0) {
                    List<String> headNameList = head.getHeadNameList();
                    excelWriteHeadProperty.setHeadRowNumber(headNameList.size() + 1);
                    List<String> headName = new ArrayList<>(headNameList.size() + 1);
                    if (head.getColumnIndex() == 0) {
                        headName.add("   ");
                    } else {
                        headName.add(this.title);
                    }
                    headName.addAll(headNameList);
                    head.setHeadNameList(headName);
                }
                Integer index = fieldToIndexMap.get(head.getFieldName());
                Integer headIndex = result.entrySet().stream().filter(Objects::nonNull)
                        .filter(x -> Objects.equals(x.getValue().getFieldName(), head.getFieldName())).findFirst()
                        .map(Map.Entry::getKey).orElse(headEntry.getKey());
                Head replaceHead = result.entrySet().stream().filter(Objects::nonNull)
                        .filter(x -> Objects.equals(x.getKey(), index)).findFirst().map(Map.Entry::getValue).orElse(null);
                if (Objects.nonNull(replaceHead)) {
                    replaceHead.setColumnIndex(headIndex);
                    result.put(headIndex, replaceHead);
                }
                head.setColumnIndex(ObjectUtils.defaultIfNull(index, headEntry.getKey()));
                result.put(ObjectUtils.defaultIfNull(index, headEntry.getKey()), head);
            }
            this.indexHeadMap = result;
            excelWriteHeadProperty.setHeadMap(result);
            writeSheetHolder.setExcelWriteHeadProperty(excelWriteHeadProperty);
            context.setWriteSheetHolder(writeSheetHolder);
        }
        this.beforeCellCreate(context.getWriteSheetHolder(), context.getWriteTableHolder(), context.getRow(),
                context.getHeadData(), context.getColumnIndex(), context.getRelativeRowIndex(), context.getHead());
    }

    /**
     * 前置处理
     *
     * @param writeSheetHolder writeSheetHolder
     * @param writeTableHolder writeTableHolder
     * @param row row
     * @param head head
     * @param columnIndex columnIndex
     * @param relativeRowIndex relativeRowIndex
     * @param isHead isHead
     */
    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row,
                                 Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
        Sheet sheet = writeSheetHolder.getSheet();
        sheet.setDefaultColumnWidth(CELL_WEIGHT);
        sheet.setColumnWidth(0, CELL_WEIGHT * WIDTH);
        sheet.setDefaultRowHeightInPoints(CELL_HEIGHT);
        if (Boolean.TRUE.equals(isHead)) {
            row.setHeightInPoints((short) HEAD_CELL_HEIGHT);
        } else {
            row.setHeightInPoints((short) CELL_HEIGHT);
        }
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
        // 设置默认logo
        if (cell.getRowIndex() == 0 && cell.getColumnIndex() == 0) {
            cellImage(workbook, cell.getSheet(), LOGO_URL, 0);
            ExcelWriteHeadProperty excelWriteHeadProperty = writeSheetHolder.getExcelWriteHeadProperty();
            if (excelWriteHeadProperty.getHeadMap().size() > TWO) {
                CellRangeAddress callRangeAddress =
                        new CellRangeAddress(0, 0, 1, excelWriteHeadProperty.getHeadMap().size() - 1);
                Sheet sheet = writeSheetHolder.getSheet();
                sheet.addMergedRegion(callRangeAddress);
            }
        }
    }

    @Override
    public void afterCellDataConverted(CellWriteHandlerContext context) {
        // 设置数据内容格式
        WriteCellData<?> firstCellData = context.getFirstCellData();
        WriteCellStyle cellStyle = firstCellData.getOrCreateStyle();
        if (Objects.equals(firstCellData.getType(), CellDataTypeEnum.NUMBER)) {
            cellStyle.setHorizontalAlignment(HorizontalAlignment.RIGHT);
        } else {
            cellStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);
        }
    }

    @Override
    public void afterCellDispose(CellWriteHandlerContext context) {
        WriteCellStyle writeCellStyle = context.getFirstCellData().getOrCreateStyle();
        WriteFont writeFont = new WriteFont();
        writeFont.setFontName("微软雅黑");
        if (Boolean.TRUE.equals(context.getHead())) {
            writeCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            writeCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND
            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            if (context.getRowIndex() == 0) {
                writeFont.setColor(IndexedColors.BROWN.index);
                writeCellStyle.setFillForegroundColor(IndexedColors.LEMON_CHIFFON.index);
            } else {
                writeFont.setColor(IndexedColors.BLACK.index);
                writeCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.index);
                setBorderStyle(writeCellStyle);
            }
            writeCellStyle.setWrapped(true);
        } else {
            writeFont.setFontHeightInPoints((short) CELL_WEIGHT);
        }
        writeCellStyle.setWriteFont(writeFont);
        if (context.getRowIndex() != 0) {
            writeCellStyle.setBorderRight(BorderStyle.THIN);
            writeCellStyle.setBorderLeft(BorderStyle.THIN);
            writeCellStyle.setBorderTop(BorderStyle.THIN);
            writeCellStyle.setBorderBottom(BorderStyle.THIN);
        }
    }

    /**
     * 写入logo
     *
     * @param imgUrl   图片的资源路径
     * @param workbook 操作book
     * @param sheet    sheet
     * @param rowIndex 行号
     */
    public void cellImage(Workbook workbook, Sheet sheet, String imgUrl, int rowIndex) {
        // 图片
        BufferedImage bufferImg;
        ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
        try {
            bufferImg = ImageIO.read(new URL(imgUrl));
            // 将图片写入流中(png变成jpg会导致图片颜色不对)
            ImageIO.write(bufferImg, "png", byteArrayOut);
            // 利用HSSFPatriarch将图片写入EXCEL
            Drawing<?> patriarch = sheet.createDrawingPatriarch();
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 0, 0, (short) 0, 0);
            // 设置图片的高度为接近行高的整数，图片宽度为高度的2倍
            int rowHeightInPoints = (int) Math.round((sheet.getRow(rowIndex).getHeightInPoints() * CONVERSION));
            anchor.setDx2(rowHeightInPoints * TWO * UNIT_OFFSET);
            anchor.setDy2(rowHeightInPoints * UNIT_OFFSET);
            anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
            patriarch.createPicture(anchor, workbook.addPicture(byteArrayOut.toByteArray(), HSSFWorkbook.PICTURE_TYPE_JPEG));
            byteArrayOut.close();
        } catch (IOException e) {
            logger.error("excel写入logo出错：msg :{}", e.getMessage(), e);
        }
    }
    private void setBorderStyle(WriteCellStyle writeCellStyle) {
        // 设置边框样式
        writeCellStyle.setBorderRight(BorderStyle.THIN);
        writeCellStyle.setBorderLeft(BorderStyle.THIN);
        writeCellStyle.setBorderTop(BorderStyle.THIN);
        writeCellStyle.setBorderBottom(BorderStyle.THIN);
    }

}
