package com.innodealing.onshore.bondservice.excel.conveter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.Objects;

/**
 * 万元转亿元保留两位小数 转换器
 *
 * <AUTHOR>
 * @date 2024/04/09
 */
public class TenThousandToBillion2ScaleConverter implements Converter<BigDecimal> {
    private static final BigDecimal PERCENT_WEIGHT = BigDecimal.valueOf(10000L);
    private static final int DEFAULT_SCALE = 2;
    @Override
    public WriteCellData<?> convertToExcelData(BigDecimal value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws ParseException {
        if (Objects.isNull(value)) {
            return null;
        }
        DecimalFormat df = new DecimalFormat("##,##0.00");
        BigDecimal billionAmount = value.divide(PERCENT_WEIGHT, DEFAULT_SCALE, RoundingMode.HALF_UP);
        df.setRoundingMode(RoundingMode.HALF_UP);
        final String format = df.format(billionAmount);
        return new WriteCellData<>(format);
    }
}
