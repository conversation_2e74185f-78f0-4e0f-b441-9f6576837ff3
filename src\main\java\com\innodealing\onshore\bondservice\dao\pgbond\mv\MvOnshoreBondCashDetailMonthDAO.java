package com.innodealing.onshore.bondservice.dao.pgbond.mv;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.innodealing.onshore.bondservice.mapper.pgbond.mv.group.MvOnshoreBondCashDetailMonthGroupMapper;
import com.innodealing.onshore.bondservice.mapper.pgbond.mv.MvOnshoreBondCashDetailMonthMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailMonthGroupDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.mv.MvOnshoreBondCashDetailMonthDO;
import com.innodealing.onshore.dao.postgresql.table.PgOperationTemplate;
import com.innodealing.onshore.service.AliasRefreshMaterializedView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

/**
 * 国内债券现金流按月统计物化视图
 *
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2024年10月22日 20:43:00
 */
@Repository
public class MvOnshoreBondCashDetailMonthDAO extends AliasRefreshMaterializedView {

    @Resource
    private MvOnshoreBondCashDetailMonthMapper mvOnshoreBondCashDetailMonthMapper;
    @Resource
    private MvOnshoreBondCashDetailMonthGroupMapper mvOnshoreBondCashDetailMonthGroupMapper;

    /**
     * 实例化
     *
     * @param pgOperationTemplate 模板类
     * @author: 张飞翔
     * @date: 2023/8/21 13:59
     */
    public MvOnshoreBondCashDetailMonthDAO(@Autowired PgOperationTemplate pgOperationTemplate) {
        super(MvOnshoreBondCashDetailMonthDO.class, pgOperationTemplate);
    }

    /**
     * 国内债券现金流按月统计物化视图
     *
     * @param comUniCodes 发行人编码
     * @return 发行统计数据
     */

    public List<MvOnshoreBondCashDetailMonthGroupDO> listMvOnshoreBondCashDetailMonthGroupDO(Collection<Long> comUniCodes) {
        GroupedQuery<MvOnshoreBondCashDetailMonthDO, MvOnshoreBondCashDetailMonthGroupDO> groupedQuery =
                GroupByQuery.createQuery(MvOnshoreBondCashDetailMonthDO.class, MvOnshoreBondCashDetailMonthGroupDO.class)
                        .and(Objects.nonNull(comUniCodes), MvOnshoreBondCashDetailMonthDO::getComUniCode, x -> x.in(comUniCodes))
                        .groupBy(MvOnshoreBondCashDetailMonthDO::getDataYear, MvOnshoreBondCashDetailMonthDO::getDataMonth);
        return mvOnshoreBondCashDetailMonthGroupMapper.selectByGroupedQuery(groupedQuery);
    }
}
