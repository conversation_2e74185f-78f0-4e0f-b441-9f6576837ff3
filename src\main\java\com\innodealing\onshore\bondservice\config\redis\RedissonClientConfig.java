package com.innodealing.onshore.bondservice.config.redis;

import com.innodealing.onshore.config.redis.RedissonClientProperties;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * RedissonClientConfig
 *
 * <AUTHOR> on 2024/1/4
 * <AUTHOR>
 */
@Configuration
public class RedissonClientConfig {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * redisson 客户端 属性
     *
     * @return RedissonClientProperties
     */
    @Bean
    @ConfigurationProperties("redisson.redis.dwdbillservice")
    @ConditionalOnMissingBean(name = "dwdBillServiceRedissonClientProperties")
    public RedissonClientProperties dwdBillServiceRedissonClientProperties() {
        return new RedissonClientProperties();
    }

    /**
     * dwdBillService redisson client
     *
     * @param redissonClientProperties RedissonClientProperties
     * @param redisProperties          RedisProperties
     * @return RedissonClient
     */
    @Bean
    @ConditionalOnMissingBean(name = "dwdBillServiceRedissonClient")
    public RedissonClient dwdBillServiceRedissonClient(@Qualifier("dwdBillServiceRedissonClientProperties") RedissonClientProperties redissonClientProperties,
                                                       RedisProperties redisProperties) {
        this.logger.info("[dwd-bill-service] RedissonClient init");
        String host;
        int port;
        int database;
        String password;
        if (StringUtils.isNotBlank(redissonClientProperties.getHost())) {
            host = redissonClientProperties.getHost();
            port = redissonClientProperties.getPort();
            database = redissonClientProperties.getDatabase();
            password = redissonClientProperties.getPassword();
        } else {
            host = redisProperties.getHost();
            port = redisProperties.getPort();
            database = redisProperties.getDatabase();
            password = redisProperties.getPassword();
        }
        Config config = new Config();
        String address = String.format("redis://%s:%d", host, port);
        SingleServerConfig singleServerConfig = config.useSingleServer().setAddress(address).setDatabase(database);
        if (StringUtils.isNoneBlank(password)) {
            singleServerConfig.setPassword(password);
        }
        return Redisson.create(config);
    }
}

