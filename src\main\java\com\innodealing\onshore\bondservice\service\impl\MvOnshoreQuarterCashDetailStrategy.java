package com.innodealing.onshore.bondservice.service.impl;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.enums.QuarterEnum;
import com.innodealing.onshore.bondservice.dao.pgbond.mv.MvOnshoreBondCashDetailQuarterDAO;
import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailQuarterGroupDO;
import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;
import com.innodealing.onshore.bondservice.service.MvOnshoreBondCashDetailStrategy;
import jodd.util.StringPool;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 季度数据查询策略实现
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@Component
public class MvOnshoreQuarterCashDetailStrategy implements MvOnshoreBondCashDetailStrategy {

    @Resource
    private MvOnshoreBondCashDetailQuarterDAO mvOnshoreBondCashDetailQuarterDAO;

    @Override
    public DateTypeEnum getSupportedDateType() {
        return DateTypeEnum.QUARTER;
    }

    @Override
    public List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataNoExercise(Set<Long> comUniCodes) {
        List<MvOnshoreBondCashDetailQuarterGroupDO> mvQuarterData =
                mvOnshoreBondCashDetailQuarterDAO.listMvOnshoreBondCashDetailQuarterGroupDO(comUniCodes);
        return convertToBondCashPayInfoDTO(mvQuarterData);
    }

    @Override
    public List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataExercise(Set<Long> comUniCodes) {
        List<MvOnshoreBondCashDetailQuarterGroupDO> mvQuarterData =
                mvOnshoreBondCashDetailQuarterDAO.listMvOnshoreBondCashDetailQuarterGroupDO(comUniCodes);
        return convertToExerciseBondCashPayInfoDTO(mvQuarterData);
    }


    /**
     * 转换为普通债券现金流DTO
     */
    private List<BondCashPayInfoDTO> convertToBondCashPayInfoDTO(List<MvOnshoreBondCashDetailQuarterGroupDO> dataList) {
        return dataList.stream().map(data -> {
            BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
            dto.setDataYear(data.getDataYear());
            dto.setDataQuarter(data.getDataQuarter());
            dto.setDataDateStr(data.getDataYear() + StringPool.DASH + ITextValueEnum.getEnum(QuarterEnum.class, data.getDataQuarter()));
            dto.setPayInterestCash(data.getTotalPayInterestCash());
            dto.setPayPrincipalCash(data.getTotalPayPrincipalCash());
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 转换为行权债券现金流DTO
     */
    private List<BondCashPayInfoDTO> convertToExerciseBondCashPayInfoDTO(List<MvOnshoreBondCashDetailQuarterGroupDO> dataList) {
        return dataList.stream().map(data -> {
            BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
            dto.setDataYear(data.getDataYear());
            dto.setDataQuarter(data.getDataQuarter());
            dto.setDataDateStr(data.getDataYear() + StringPool.DASH + ITextValueEnum.getEnum(QuarterEnum.class, data.getDataQuarter()));
            dto.setPayInterestCash(data.getTotalExercisePayInterestCash());
            dto.setPayPrincipalCash(data.getTotalExercisePayPrincipalCash());
            return dto;
        }).collect(Collectors.toList());
    }
} 