package com.innodealing.onshore.bondservice.model.entity.pgbond.group;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * 国内债券现金流聚合表表实体对象
 *
 * <AUTHOR>
 */
public class OnshoreBondCashDetailGroupDO {
    /**
     * 债券唯一代码
     */
    @Column
    private Long bondUniCode;
    /**
     * 数据年份
     */
    @Column
    private Integer dataYear;
    /**
     * 数据月份
     */
    @Column
    private Integer dataMonth;
    /**
     * 季度(1,2,3,4: 代表4个季度), 对应枚举QuarterEnum
     */
    @Column
    private Integer dataQuarter;
    /**
     * 偿付利息(万)
     */
    @Column(name="sum(pay_interest_cash)")
    private BigDecimal totalPayInterestCash;
    /**
     * 偿付本金(万)
     */
    @Column(name="sum(pay_principal_cash)")
    private BigDecimal totalPayPrincipalCash;
    /**
     * 行权偿付利息(万)
     */
    @Column(name="sum(exercise_pay_interest_cash)")
    private BigDecimal totalExercisePayInterestCash;
    /**
     * 行权偿付本金(万)
     */
    @Column(name="sum(exercise_pay_principal_cash)")
    private BigDecimal totalExercisePayPrincipalCash;


    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }

    public Integer getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(Integer dataMonth) {
        this.dataMonth = dataMonth;
    }

    public Integer getDataQuarter() {
        return dataQuarter;
    }

    public void setDataQuarter(Integer dataQuarter) {
        this.dataQuarter = dataQuarter;
    }

    public BigDecimal getTotalPayInterestCash() {
        return totalPayInterestCash;
    }

    public void setTotalPayInterestCash(BigDecimal totalPayInterestCash) {
        this.totalPayInterestCash = totalPayInterestCash;
    }

    public BigDecimal getTotalPayPrincipalCash() {
        return totalPayPrincipalCash;
    }

    public void setTotalPayPrincipalCash(BigDecimal totalPayPrincipalCash) {
        this.totalPayPrincipalCash = totalPayPrincipalCash;
    }

    public BigDecimal getTotalExercisePayInterestCash() {
        return totalExercisePayInterestCash;
    }

    public void setTotalExercisePayInterestCash(BigDecimal totalExercisePayInterestCash) {
        this.totalExercisePayInterestCash = totalExercisePayInterestCash;
    }

    public BigDecimal getTotalExercisePayPrincipalCash() {
        return totalExercisePayPrincipalCash;
    }

    public void setTotalExercisePayPrincipalCash(BigDecimal totalExercisePayPrincipalCash) {
        this.totalExercisePayPrincipalCash = totalExercisePayPrincipalCash;
    }
}

