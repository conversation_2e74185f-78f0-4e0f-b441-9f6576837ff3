package com.innodealing.onshore.bondservice.model.bo;

import com.google.common.collect.Maps;
import com.innodealing.onshore.bondmetadata.dto.bond.valuation.CsBondValuationBusinessDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondGuaranteeDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondIssueAgencyInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComBondDetailDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.chinabond.DwdCbValuationDTO;
import com.innodealing.onshore.bondmetadata.dto.dmdataproduct.BondBasicDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondIssueDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondTypeDTO;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <Description>
 *
 * <AUTHOR> on 2023/10/30
 * <AUTHOR>
 */
public class FetchBondInfoAndPriceBO {
    /**
     * bondInfoMap
     */
    private Map<Long, OnshoreBondInfoDTO> bondInfoMap;
    /**
     * 新 债券基础接口
     */
    private Map<Long, BondBasicDTO> bondBasicMap;
    /**
     *获取债券类型信息
     */
    private Map<Long, BondTypeDTO> bondTypeDTOMap;
    /**
     *获取债券含权状态信息
     */
    private Map<Long, BondOptionStatusDTO> bondOptionStatusDTOMap;
    /**
     * 获取债券发行信息
     */
    private Map<Long, BondIssueDTO> bondIssueDTOMap;
    /**
     * 获取中债估值数据-行权
     */
    private Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExerciseDTOMap;
    /**
     * 获取中债估值数据-到期
     */
    private Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExpireDTOMap;
    /**
     * 获取中证估值数据-行权
     */
    private Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExerciseDTOMap;
    /**
     * 获取中证估值数据-到期
     */
    private Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExpireDTOMap;
    /**
     * 获取中证估值数据-其他
     */
    private Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessOtherDTOMap;
    /**
     * 债券担保
     */
    private Map<Long, List<BondGuaranteeDTO>> bondGuaranteeMap;
    /**
     *  债券发行中介
     */
    private Map<Long, BondIssueAgencyInfoDTO> bondIssueAgencyInfoMap;
    /**
     * 获取债券发行中介信息
     */
    private Map<Long, ComBondDetailDTO> comBondDetailDTOMap;



    public Map<Long, OnshoreBondInfoDTO> getBondInfoMap() {
        return Objects.isNull(bondInfoMap) ? Maps.newHashMap() : new HashMap<>(bondInfoMap);
    }

    public void setBondInfoMap(Map<Long, OnshoreBondInfoDTO> bondInfoMap) {
        this.bondInfoMap = Objects.isNull(bondInfoMap) ? Maps.newHashMap() : new HashMap<>(bondInfoMap);
    }

    public Map<Long, BondBasicDTO> getBondBasicMap() {
        return bondBasicMap;
    }

    public void setBondBasicMap(Map<Long, BondBasicDTO> bondBasicMap) {
        this.bondBasicMap = bondBasicMap;
    }

    public Map<Long, BondTypeDTO> getBondTypeDTOMap() {
        return bondTypeDTOMap;
    }

    public void setBondTypeDTOMap(Map<Long, BondTypeDTO> bondTypeDTOMap) {
        this.bondTypeDTOMap = bondTypeDTOMap;
    }

    public Map<Long, BondOptionStatusDTO> getBondOptionStatusDTOMap() {
        return bondOptionStatusDTOMap;
    }

    public void setBondOptionStatusDTOMap(Map<Long, BondOptionStatusDTO> bondOptionStatusDTOMap) {
        this.bondOptionStatusDTOMap = bondOptionStatusDTOMap;
    }

    public Map<Long, BondIssueDTO> getBondIssueDTOMap() {
        return bondIssueDTOMap;
    }

    public void setBondIssueDTOMap(Map<Long, BondIssueDTO> bondIssueDTOMap) {
        this.bondIssueDTOMap = bondIssueDTOMap;
    }

    public Map<Long, DwdCbValuationDTO> getDwdCbValuationBusinessExerciseDTOMap() {
        return dwdCbValuationBusinessExerciseDTOMap;
    }

    public void setDwdCbValuationBusinessExerciseDTOMap(Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExerciseDTOMap) {
        this.dwdCbValuationBusinessExerciseDTOMap = dwdCbValuationBusinessExerciseDTOMap;
    }

    public Map<Long, DwdCbValuationDTO> getDwdCbValuationBusinessExpireDTOMap() {
        return dwdCbValuationBusinessExpireDTOMap;
    }

    public void setDwdCbValuationBusinessExpireDTOMap(Map<Long, DwdCbValuationDTO> dwdCbValuationBusinessExpireDTOMap) {
        this.dwdCbValuationBusinessExpireDTOMap = dwdCbValuationBusinessExpireDTOMap;
    }

    public Map<Long, CsBondValuationBusinessDTO> getCsBondValuationBusinessExerciseDTOMap() {
        return csBondValuationBusinessExerciseDTOMap;
    }

    public void setCsBondValuationBusinessExerciseDTOMap(Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExerciseDTOMap) {
        this.csBondValuationBusinessExerciseDTOMap = csBondValuationBusinessExerciseDTOMap;
    }

    public Map<Long, CsBondValuationBusinessDTO> getCsBondValuationBusinessExpireDTOMap() {
        return csBondValuationBusinessExpireDTOMap;
    }

    public void setCsBondValuationBusinessExpireDTOMap(Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessExpireDTOMap) {
        this.csBondValuationBusinessExpireDTOMap = csBondValuationBusinessExpireDTOMap;
    }

    public Map<Long, List<BondGuaranteeDTO>> getBondGuaranteeMap() {
        return bondGuaranteeMap;
    }

    public void setBondGuaranteeMap(Map<Long, List<BondGuaranteeDTO>> bondGuaranteeMap) {
        this.bondGuaranteeMap = bondGuaranteeMap;
    }

    public Map<Long, BondIssueAgencyInfoDTO> getBondIssueAgencyInfoMap() {
        return bondIssueAgencyInfoMap;
    }

    public void setBondIssueAgencyInfoMap(Map<Long, BondIssueAgencyInfoDTO> bondIssueAgencyInfoMap) {
        this.bondIssueAgencyInfoMap = bondIssueAgencyInfoMap;
    }

    public Map<Long, ComBondDetailDTO> getComBondDetailDTOMap() {
        return comBondDetailDTOMap;
    }

    public void setComBondDetailDTOMap(Map<Long, ComBondDetailDTO> comBondDetailDTOMap) {
        this.comBondDetailDTOMap = comBondDetailDTOMap;
    }

    public Map<Long, CsBondValuationBusinessDTO> getCsBondValuationBusinessOtherDTOMap() {
        return csBondValuationBusinessOtherDTOMap;
    }

    public void setCsBondValuationBusinessOtherDTOMap(Map<Long, CsBondValuationBusinessDTO> csBondValuationBusinessOtherDTOMap) {
        this.csBondValuationBusinessOtherDTOMap = csBondValuationBusinessOtherDTOMap;
    }
}
