package com.innodealing.onshore.bondservice.model.entity.bond;

import javax.persistence.*;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 国内债券现金流聚合表表实体对象
 *
 * <AUTHOR>
 */
@Table(name = "onshore_bond_cash_detail")
public class OnshoreBondCashDetailDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 发行人唯一代码
     */
    @Column
    private Long comUniCode;
    /**
     * 债券唯一代码
     */
    @Column
    private Long bondUniCode;
    /**
     * 数据日期
     */
    @Column
    private Date dataDate;
    /**
     * 数据年份
     */
    @Column
    private Integer dataYear;
    /**
     * 数据月份
     */
    @Column
    private Integer dataMonth;
    /**
     * 季度(1,2,3,4: 代表4个季度), 对应枚举QuarterEnum
     */
    @Column
    private Integer dataQuarter;
    /**
     * 偿付利息(万)
     */
    @Column
    private BigDecimal payInterestCash;
    /**
     * 偿付本金(万)
     */
    @Column
    private BigDecimal payPrincipalCash;
    /**
     * 含权偿付利息(万)
     */
    @Column
    private BigDecimal exercisePayInterestCash;
    /**
     * 含权偿付本金(万)
     */
    @Column
    private BigDecimal exercisePayPrincipalCash;
    /**
     * 理论付息日(万)
     */
    @Column
    private Date theoryInterestDate;
    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }


    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }


    public Date getDataDate() {
        return dataDate;
    }

    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }


    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }


    public Integer getDataMonth() {
        return dataMonth;
    }

    public void setDataMonth(Integer dataMonth) {
        this.dataMonth = dataMonth;
    }


    public Integer getDataQuarter() {
        return dataQuarter;
    }

    public void setDataQuarter(Integer dataQuarter) {
        this.dataQuarter = dataQuarter;
    }


    public BigDecimal getPayInterestCash() {
        return payInterestCash;
    }

    public void setPayInterestCash(BigDecimal payInterestCash) {
        this.payInterestCash = payInterestCash;
    }


    public BigDecimal getPayPrincipalCash() {
        return payPrincipalCash;
    }

    public void setPayPrincipalCash(BigDecimal payPrincipalCash) {
        this.payPrincipalCash = payPrincipalCash;
    }


    public BigDecimal getExercisePayInterestCash() {
        return exercisePayInterestCash;
    }

    public void setExercisePayInterestCash(BigDecimal exercisePayInterestCash) {
        this.exercisePayInterestCash = exercisePayInterestCash;
    }


    public BigDecimal getExercisePayPrincipalCash() {
        return exercisePayPrincipalCash;
    }

    public void setExercisePayPrincipalCash(BigDecimal exercisePayPrincipalCash) {
        this.exercisePayPrincipalCash = exercisePayPrincipalCash;
    }


    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }


    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }

    public Date getTheoryInterestDate() {
        return theoryInterestDate;
    }

    public void setTheoryInterestDate(Date theoryInterestDate) {
        this.theoryInterestDate = theoryInterestDate;
    }
}

