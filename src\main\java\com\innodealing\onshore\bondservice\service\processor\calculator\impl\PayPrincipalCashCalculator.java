package com.innodealing.onshore.bondservice.service.processor.calculator.impl;

import com.innodealing.onshore.bondservice.config.constant.NumberConstant;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.service.processor.calculator.AbstractBondCashDetailCalculator;
import com.innodealing.onshore.bondservice.utils.BondCashCalculationUtils;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * 偿付本金 计算器
 * 负责计算偿付本金字段
 *
 * <AUTHOR>
 */
@Component
public class PayPrincipalCashCalculator extends AbstractBondCashDetailCalculator {


    @Override
    public String getName() {
        return PayPrincipalCashCalculator.class.getName();
    }

    @Override
    public int getPriority() {
        return NumberConstant.INT_TWO;
    }

    @Override
    protected void doCalculate(BondProcessContext context, Long bondUniCode) {
        // 使用工具类进行数据验证
        if (!BondCashCalculationUtils.validateContextData(context, bondUniCode, PayPrincipalCashCalculator.class.getName())) {
            return;
        }
        // 使用父类公共常量和工具类方法进行计算
        BondCashCalculationUtils.processNonRightBondPrincipal(
                context,
                bondUniCode,
                BondCashCalculationUtils.groupBondAmountsByKey(
                        context.getBondAmountMap().getOrDefault(bondUniCode, Collections.emptyList())
                                .stream()
                                .filter(bondAmount -> FILTER_CHANGE_REASONS.contains(bondAmount.getChangeReasonCode()))
                                .collect(java.util.stream.Collectors.toList()),
                        this::getBondAmountKey
                ),
                OnshoreBondCashDetailBO::setPayPrincipalCash
        );
    }
}
