package com.innodealing.onshore.bondservice.model.entity.pgbond.mv;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 国内债券现金流按季度统计物化视图
 *
 * <AUTHOR>
 */
@Table(name = "mv_onshore_bond_cash_detail_quarter")
public class MvOnshoreBondCashDetailQuarterDO {
    /**
     * 发行人唯一代码
     */
    @Column
    private Long comUniCode;
    /**
     * 数据年份
     */
    @Column
    private Integer dataYear;
    /**
     * 季度(1,2,3,4: 代表4个季度), 对应枚举QuarterEnum
     */
    @Column
    private Integer dataQuarter;
    /**
     * 偿付利息(万)
     */
    @Column
    private BigDecimal totalPayInterestCash;
    /**
     * 偿付本金(万)
     */
    @Column
    private BigDecimal totalPayPrincipalCash;
    /**
     * 行权偿付利息(万)
     */
    @Column
    private BigDecimal totalExercisePayInterestCash;
    /**
     * 行权偿付本金(万)
     */
    @Column
    private BigDecimal totalExercisePayPrincipalCash;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getDataYear() {
        return dataYear;
    }

    public void setDataYear(Integer dataYear) {
        this.dataYear = dataYear;
    }

    public Integer getDataQuarter() {
        return dataQuarter;
    }

    public void setDataQuarter(Integer dataQuarter) {
        this.dataQuarter = dataQuarter;
    }

    public BigDecimal getTotalPayInterestCash() {
        return totalPayInterestCash;
    }

    public void setTotalPayInterestCash(BigDecimal totalPayInterestCash) {
        this.totalPayInterestCash = totalPayInterestCash;
    }

    public BigDecimal getTotalPayPrincipalCash() {
        return totalPayPrincipalCash;
    }

    public void setTotalPayPrincipalCash(BigDecimal totalPayPrincipalCash) {
        this.totalPayPrincipalCash = totalPayPrincipalCash;
    }

    public BigDecimal getTotalExercisePayInterestCash() {
        return totalExercisePayInterestCash;
    }

    public void setTotalExercisePayInterestCash(BigDecimal totalExercisePayInterestCash) {
        this.totalExercisePayInterestCash = totalExercisePayInterestCash;
    }

    public BigDecimal getTotalExercisePayPrincipalCash() {
        return totalExercisePayPrincipalCash;
    }

    public void setTotalExercisePayPrincipalCash(BigDecimal totalExercisePayPrincipalCash) {
        this.totalExercisePayPrincipalCash = totalExercisePayPrincipalCash;
    }
}

