package com.innodealing.onshore.bondservice.service;

import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 债券数据查询策略工厂
 * 支持策略自动注册机制，扩展新的时间类型只需添加对应的策略实现类
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
@Component
public class MvOnshoreBondCashDetailStrategyFactory {

    private static final Logger log = LoggerFactory.getLogger(MvOnshoreBondCashDetailStrategyFactory.class);
    @Resource
    private ApplicationContext applicationContext;

    /**
     * 策略映射表：时间类型枚举 -> 对应的策略实现
     */
    private final Map<DateTypeEnum, MvOnshoreBondCashDetailStrategy> strategyMap = new ConcurrentHashMap<>();

    /**
     * Spring容器初始化完成后，自动发现和注册所有策略实现
     */
    @PostConstruct
    private void initializeStrategies() {
        Map<String, MvOnshoreBondCashDetailStrategy> strategies = applicationContext.getBeansOfType(MvOnshoreBondCashDetailStrategy.class);
        // 遍历策略实现，建立时间类型到策略的映射关系
        strategies.values().forEach(strategy -> {
            DateTypeEnum supportedDateTypeEnum = strategy.getSupportedDateType();
            if (supportedDateTypeEnum != null) {
                strategyMap.put(supportedDateTypeEnum, strategy);
            }
        });
        log.info("MvOnshoreBondCashDetailStrategyFactory register size: {}", strategyMap.size());
    }

    /**
     * 根据时间类型枚举获取对应的查询策略（推荐使用）
     *
     * @param dateTypeEnum 时间类型枚举
     * @return 对应的查询策略
     * @throws IllegalArgumentException 当传入不支持的时间类型时
     */
    public MvOnshoreBondCashDetailStrategy getStrategy(DateTypeEnum dateTypeEnum) {
        if (dateTypeEnum == null) {
            throw new IllegalArgumentException("时间类型不能为空");
        }
        MvOnshoreBondCashDetailStrategy strategy = strategyMap.get(dateTypeEnum);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的时间类型: " + dateTypeEnum);
        }
        return strategy;
    }
}