package com.innodealing.onshore.bondservice.model.dto.request;


import io.swagger.annotations.ApiModelProperty;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondCashDistributionHistogramRequestDTO {

    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("时间类型：1.年 2.季 3.月")
    private Integer dateType;
    @ApiModelProperty("相同实控人状态 0.否 1.是 ")
    private Integer ctrlTypeStatus;
    @ApiModelProperty("作为资产支持证券的共同债务人状态 0.否 1.是")
    private Integer absTypeStatus;
    @ApiModelProperty("行权状态 0.否 1.是")
    private Integer exerciseStatus;


    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getDateType() {
        return dateType;
    }

    public void setDateType(Integer dateType) {
        this.dateType = dateType;
    }

    public Integer getCtrlTypeStatus() {
        return ctrlTypeStatus;
    }

    public void setCtrlTypeStatus(Integer ctrlTypeStatus) {
        this.ctrlTypeStatus = ctrlTypeStatus;
    }

    public Integer getAbsTypeStatus() {
        return absTypeStatus;
    }

    public void setAbsTypeStatus(Integer absTypeStatus) {
        this.absTypeStatus = absTypeStatus;
    }

    public Integer getExerciseStatus() {
        return exerciseStatus;
    }

    public void setExerciseStatus(Integer exerciseStatus) {
        this.exerciseStatus = exerciseStatus;
    }
}
