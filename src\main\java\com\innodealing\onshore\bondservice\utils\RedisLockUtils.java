package com.innodealing.onshore.bondservice.utils;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.function.Supplier;

/**
 * redis分布式锁 工具类
 *
 * <AUTHOR>
 */
@Component
@SuppressWarnings("squid:S2222")
public class RedisLockUtils {
    @Resource
    private RedissonClient redissonClient;

    /**
     * 分布式锁操作
     *
     * @param key           redisKey
     * @param failParameter 抢占锁执行失败参数
     * @param supplier 工作
     * @return 工作结果
     */
    public <T> T lock(String key, T failParameter, Supplier<T> supplier) {
        RLock lock = redissonClient.getLock(key);
        if (!lock.tryLock()) {
            return failParameter;
        }
        try {
            return supplier.get();
        } finally {
            if (lock.isHeldByCurrentThread() && lock.isLocked()) {
                lock.unlock();
            }
        }
    }

}
