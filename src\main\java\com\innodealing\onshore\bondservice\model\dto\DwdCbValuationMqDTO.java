package com.innodealing.onshore.bondservice.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 中债估值
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class DwdCbValuationMqDTO {
    @ApiModelProperty("债券唯一编码")
    private Long bondUniCode;
    @ApiModelProperty("估值日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date valuationDate;
    @ApiModelProperty("估价收益率(%)")
    private BigDecimal valuationYield;
    @ApiModelProperty("估值类型 1到期 2行权")
    private Integer valuationType;

    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Date getValuationDate() {
        return valuationDate;
    }

    public void setValuationDate(Date valuationDate) {
        this.valuationDate = valuationDate;
    }

    public BigDecimal getValuationYield() {
        return valuationYield;
    }

    public void setValuationYield(BigDecimal valuationYield) {
        this.valuationYield = valuationYield;
    }

    public Integer getValuationType() {
        return valuationType;
    }

    public void setValuationType(Integer valuationType) {
        this.valuationType = valuationType;
    }
}