<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.bondservice.mapper.bond.OnshoreBondFilterMapper">

    <select id="listCrossMarketDedupStatusBondUniCodes" resultType="java.lang.Long">
        SELECT bond_uni_code
        FROM (SELECT bond_uni_code, ROW_NUMBER() OVER (PARTITION BY bond_id ORDER BY
        CASE second_market
        WHEN 3 THEN 1
        WHEN 2 THEN 2
        WHEN 1 THEN 3
        WHEN 78 THEN 4
        ELSE 999
        END) as rn
        FROM onshore_bond_filter WHERE 1=1
        <if test="comUniCodes != null and comUniCodes.size() > 0">
            and com_uni_code IN
            <foreach item="item" collection="comUniCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bondUniCodes != null and bondUniCodes.size() > 0">
            and bond_uni_code IN
            <foreach item="item" collection="bondUniCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="secondMarkets != null and secondMarkets.size() > 0">
            and second_market IN
            <foreach item="item" collection="secondMarkets" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) ranked
        WHERE rn = 1;
    </select>


    <select id="selectOnshoreBondFilterStatistics" resultType="com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView"
            parameterType="com.innodealing.onshore.bondservice.model.bo.BondBalanceStatisticsBO">
        select
            <if test="crossMarketDedupStatus == null or crossMarketDedupStatus == 0">
                sum(no_market_outstanding_status_bond_count) duration_count,
                sum(no_market_expired_bond_count) expired_count,
            </if>
            <if test="crossMarketDedupStatus == 1">
                COUNT(CASE WHEN outstanding_status = 1 THEN 1 END) duration_count,
                COUNT(CASE WHEN expired = 1 THEN 1 END) expired_count,
            </if>
            SUM(CASE WHEN outstanding_status = 1 THEN bond_balance ELSE 0 END) duration_total_bond_balance,
            SUM(CASE WHEN expired = 1 THEN bond_balance ELSE 0 END) expired_total_bond_balance
        from (
             <include refid="selectOnshoreBondFilterStatistics"/>
             ) as temp;
    </select>


    <sql  id="selectOnshoreBondFilterStatistics" >
        SELECT
        count(CASE WHEN outstanding_status = 1 THEN 1 END) as no_market_outstanding_status_bond_count,
        count(CASE WHEN expired = 1 THEN 1 END) as no_market_expired_bond_count,
        outstanding_status,
        bond_balance,
        expired
        FROM onshore_bond_filter
        WHERE deleted = 0
        <!-- 过滤发行状态：排除延迟发行(2)、取消发行(3)、其他(999) -->
        AND issue_status NOT IN (2, 3, 999)

        <!-- 主体唯一编码过滤 -->
        <if test="comUniCodes != null and comUniCodes.size() > 0">
            AND com_uni_code IN
            <foreach collection="comUniCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <!-- 债券唯一编码过滤 -->
        <if test="bondUniCodes != null and bondUniCodes.size() > 0">
            AND bond_uni_code IN
            <foreach collection="bondUniCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <!-- 债券类型过滤 -->
        <if test="bondTypes != null and bondTypes.size() > 0">
            AND bond_type IN
            <foreach collection="bondTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <if test="secondMarkets != null and secondMarkets.size > 0">
            AND second_market IN
            <foreach collection="secondMarkets" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <!-- 募集方式过滤 -->
        <if test="publicOfferings != null and publicOfferings.size() > 0">
            AND public_offering IN
            <foreach collection="publicOfferings" item="offering" open="(" separator="," close=")">
                #{offering}
            </foreach>
        </if>

        <!-- 债券状态过滤 -->
        <if test="bondStatus != null and bondStatus.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <!-- 存续债券 -->
                <if test="bondStatus.contains(17)">
                    OR outstanding_status = 1
                </if>
                <!-- 到期债券 -->
                <if test="bondStatus.contains(18)">
                    OR expired = 1
                </if>
                <!-- 发行中债券 -->
                <if test="bondStatus.contains(0)">
                    OR issue_status = 0
                </if>
            </trim>
            )
        </if>

        <!-- 债券条款过滤 -->
        <if test="bondTerms != null and bondTerms.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <!-- 永续债券 -->
                <if test="bondTerms.contains(108)">
                    OR embedded_option = 2
                </if>
                <!-- 回售债券 -->
                <if test="bondTerms.contains(106)">
                    OR put_option_status = 1
                </if>
                <!-- 赎回债券 -->
                <if test="bondTerms.contains(105)">
                    OR redeem_status = 1
                </if>
                <!-- 票面利率选择权 -->
                <if test="bondTerms.contains(12)">
                    OR coupon_adjustable_status = 1
                </if>
            </trim>
            )
        </if>
        GROUP BY bond_id
    </sql>
</mapper>