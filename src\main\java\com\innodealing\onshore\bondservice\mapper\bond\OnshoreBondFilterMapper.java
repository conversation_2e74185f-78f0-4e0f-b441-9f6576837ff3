package com.innodealing.onshore.bondservice.mapper.bond;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.bondservice.model.bo.BondBalanceStatisticsBO;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 基础筛选mapper
 *
 * <AUTHOR>
 */
public interface OnshoreBondFilterMapper extends DynamicQueryMapper<OnshoreBondFilterDO> {


    /**
     * 获取跨市场债券code
     *
     * @param comUniCodes 主体code
     * @param secondMarkets 二级市场
     * @return {@link List }<{@link Long }>
     */
    List<Long> listCrossMarketDedupStatusBondUniCodes(@Param("comUniCodes") Collection<Long> comUniCodes,
                                                      @Param("bondUniCodes") Collection<Long> bondUniCodes,
                                                      @Param("secondMarkets") Collection<Integer> secondMarkets);


    /**
     * 获取跨市场债券统计信息
     *
     * @return {@link OnshoreBondFilterView}
     */
    OnshoreBondFilterView selectOnshoreBondFilterStatistics(BondBalanceStatisticsBO req);
}
