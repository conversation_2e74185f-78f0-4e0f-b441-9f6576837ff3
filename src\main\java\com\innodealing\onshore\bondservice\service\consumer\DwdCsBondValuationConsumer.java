package com.innodealing.onshore.bondservice.service.consumer;


import com.fasterxml.jackson.core.type.TypeReference;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.mq.MqMessageUtils;
import com.innodealing.kafka.annotation.HermesKafkaListener;
import com.innodealing.kafka.listener.HermesBatchOrderlyListener;
import com.innodealing.kafka.model.KafkaMessage;
import com.innodealing.onshore.bondmetadata.bo.rocketmq.CanalFlatMessageBO;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondservice.config.constant.KafkaConstant;
import com.innodealing.onshore.bondservice.model.dto.CsBondValuationMqDTO;
import com.innodealing.onshore.bondservice.service.BondFilterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

import static com.innodealing.onshore.bondservice.config.constant.KafkaConstant.DWD_BOND_VALUATION_DWD_CS_BOND_VALUATION_TOPIC;

/**
 * dwd 中证消费者
 *
 * <AUTHOR>
 * @create: 2025-01-21
 */
@Service
@HermesKafkaListener(
        topics = "${POLARDB_PROFILES_ACTIVE:}" + DWD_BOND_VALUATION_DWD_CS_BOND_VALUATION_TOPIC,
        groupId ="${POLARDB_PROFILES_ACTIVE:}" + KafkaConstant.KAFKA_DEFAULT_GROUP + "-cs-onshore_bond_filter")
public class DwdCsBondValuationConsumer extends MqBaseConsumer implements HermesBatchOrderlyListener {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private BondFilterService bondFilterService;

    @Override
    public void onMessage(List<KafkaMessage> kafkaMessageList) {
        if (this.getMqSkipStatus()) {
            return;
        }
        String messageBodyJson = JSON.toJSONString(kafkaMessageList);
        try {
            List<CanalFlatMessageBO<CsBondValuationMqDTO>> canalFlatMessageList = new ArrayList<>();
            for (KafkaMessage kafkaMessage : kafkaMessageList) {
                MqMessageUtils.parseMessage(kafkaMessage.getMessageKey(), kafkaMessage.getContent(), new TypeReference<CanalFlatMessageBO<CsBondValuationMqDTO>>() {
                }).ifPresent(mqMessage -> canalFlatMessageList.add(mqMessage.getMessageBody()));
            }
            bondFilterService.syncDwdCsValuationByMq(canalFlatMessageList);
        } catch (Throwable ex) {
            logger.error(String.format("consumeMessage error message: %s ", messageBodyJson), ex);
            throw new BusinessException(JSON.toJSONString(ex));
        }
    }
}
