package com.innodealing.onshore.bondservice.controller.internal;

import com.innodealing.onshore.service.CacheOptionalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

import static com.innodealing.onshore.bondservice.config.constant.RedisKeyConstant.REDIS_KEY_MQ_MESSAGE_SKIP_STATUS;

/**
 * kafka 消费者跳过消息控制
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2025年02月07日 11:22:00
 */
@Api(tags = "(内部)消费者跳过消息控制")
@RestController
@RequestMapping("internal/kafka")
public class InternalMqSkipPositionController {

    @Resource
    private CacheOptionalService cacheOptionalService;

    @ApiOperation(value = "mq设置过滤状态")
    @PostMapping("/skip/position")
    public void skipPosition(
            @ApiParam(value = "类名") @RequestParam("className") String className,
            @ApiParam(value = "是否开启过滤： 1开启消息过滤  0 不过滤消息") @RequestParam("mqSkipStatus") Integer mqSkipStatus,
            @ApiParam(value = "安全确认码，必须为'CONFIRM_SKIP'") @RequestParam("confirmCode") String confirmCode) {
        if (!"CONFIRM_SKIP".equals(confirmCode)) {
            throw new IllegalArgumentException("确认码不正确，为防止误操作，请输入正确的确认码：CONFIRM_SKIP");
        }
        String key = cacheOptionalService.getKey(REDIS_KEY_MQ_MESSAGE_SKIP_STATUS, className);
        cacheOptionalService.setAndExpireToday(key, mqSkipStatus);
    }
}
