package com.innodealing.onshore.bondservice.mapper.pgbond;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.PgOnshoreBondCashDetailDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.OnshoreBondCashDetailGroupDO;

/**
 * 国内债券现金流按月统计物化视图
 *
 * <AUTHOR>
 */
public interface PgOnshoreBondCashDetailGroupMapper extends SelectByGroupedQueryMapper<PgOnshoreBondCashDetailDO, OnshoreBondCashDetailGroupDO> {
}