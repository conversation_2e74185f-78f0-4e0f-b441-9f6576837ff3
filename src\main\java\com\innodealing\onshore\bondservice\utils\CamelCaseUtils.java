package com.innodealing.onshore.bondservice.utils;

import com.google.common.base.CaseFormat;

/**
 * 驼峰与下划线之间互转工具类
 *
 * <AUTHOR>
 * @date 2022/07/05
 */
public final class CamelCaseUtils {

    private CamelCaseUtils() {

    }

    /**
     * 下划线换为驼峰
     * user_name to userName
     *
     * @param covertStr 待转换str
     * @return {@code String}
     */
    public static String convertUnderLineToHump(String covertStr) {
        return CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, covertStr);
    }

    /**
     * 中划线换为驼峰
     * user-name to userName
     *
     * @param covertStr 待转换str
     * @return {@code String}
     */
    public static String convertHyphenLineToHump(String covertStr) {
        return CaseFormat.LOWER_HYPHEN.to(CaseFormat.LOWER_CAMEL, covertStr);
    }

    /**
     * 驼峰转下划线
     * userName to user_name
     *
     * @param hump 驼峰
     * @return {@code String}
     */
    public static String convertHumpToUnderLine(String hump) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, hump);
    }

    /**
     * 驼峰转换为中划线
     * userName to user-name
     *
     * @param hump 驼峰
     * @return {@code String}
     */
    public static String convertHumpToHyphenLine(String hump) {
        return CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_HYPHEN, hump);
    }

}
