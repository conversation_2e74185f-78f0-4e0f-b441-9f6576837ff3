package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondabs.AbsBondComDetailInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondabs.AbsBondDetailInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.bondabs.request.AbsBondDetailBaseInfoRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.bondabs.request.AbsComBondDetailBaseInfoRequestDTO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "onshore-bond-abs", url = "${bond.abs.api.url}", path = "/internal")
public interface OnshoreBondAbsHttpService {

    @ApiOperation(value = "根据债券唯一编码批量获取ABS债券的基础分类详细信息")
    @PostMapping(value = "/abs/bondDetailInfo/batch/bond-uni-code")
    List<AbsBondDetailInfoDTO> listAbsBondDetailInfoResponseDTOs(@RequestBody AbsBondDetailBaseInfoRequestDTO request);

    @ApiOperation(value = "根据主体唯一编码批量获取ABS债券的基础分类详细信息")
    @PostMapping(value = "/abs/bondDetailInfo/batch/com-uni-code")
    List<AbsBondComDetailInfoDTO> listAbsAbsBondComDetailInfoDTOResponseDTOs(@RequestBody AbsComBondDetailBaseInfoRequestDTO request);




}
