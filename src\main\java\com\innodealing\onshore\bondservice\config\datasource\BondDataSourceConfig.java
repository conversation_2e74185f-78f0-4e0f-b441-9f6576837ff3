package com.innodealing.onshore.bondservice.config.datasource;

import com.alibaba.druid.pool.DruidDataSource;
import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;
import tk.mybatis.spring.annotation.MapperScan;

import javax.sql.DataSource;

/**
 * bond 数据源配置
 *
 * <AUTHOR>
 * @date 2025/06/16
 **/
@Configuration
@MapperScan(basePackages = {"com.innodealing.onshore.bondservice.mapper.bond"},
        sqlSessionFactoryRef = BondDataSourceConfig.SESSION_FACTORY_NAME)
public class BondDataSourceConfig extends BaseSourceConfig {
    public static final String TRANSACTION_NAME = "bondTransactionManager";

    public static final String DATA_SOURCE_NAME = "bondDataSource";
    public static final String DATA_SOURCE_PREFIX = "bond.datasource";
    public static final String SESSION_FACTORY_NAME = "bondSqlSessionFactory";
    public static final String TRANSACTION_TEMPLATE_NAME = "bondTransactionTemplate";
    protected static final String[] ALIAS_PACKAGES = {"com.innodealing.onshore.bondservice.model.entity.bond"};
    public static final String DATABASE_NAME = "bond";


    /**
     * 创建数据源
     *
     * @return 返回数据源
     */
    @Bean(name = DATA_SOURCE_NAME, initMethod = "init", destroyMethod = "close")
    @ConfigurationProperties(prefix = DATA_SOURCE_PREFIX)
    public DruidDataSource dataSource() {
        DruidDataSource build = DruidDataSourceBuilder.create().build();
        build.setName(DATABASE_NAME);
        return build;
    }

    /**
     * 配置事务
     *
     * @return 事务
     */
    @Bean(name = TRANSACTION_NAME)
    public DataSourceTransactionManager transactionManager() {
        return new DataSourceTransactionManager(dataSource());
    }

    /**
     * 创建SqlSessionFactory对象
     *
     * @param dataSource 数据源
     * @return SqlSessionFactory对象
     * @throws Exception 异常
     */
    @Bean(name = SESSION_FACTORY_NAME)
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Qualifier(DATA_SOURCE_NAME) DataSource dataSource) throws Exception {
        return super.getSessionFactory(dataSource, ALIAS_PACKAGES);
    }

    /**
     * 事务模板
     *
     * @param transactionManager 事务管理器
     * @return 事务模板
     */
    @Bean(name = TRANSACTION_TEMPLATE_NAME)
    @Primary
    public TransactionTemplate transactionTemplate(@Qualifier(TRANSACTION_NAME) DataSourceTransactionManager transactionManager) {
        return new TransactionTemplate(transactionManager);
    }
}
