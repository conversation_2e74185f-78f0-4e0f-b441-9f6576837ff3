package com.innodealing.onshore.bondservice.controller.internal;


import com.innodealing.onshore.bondservice.service.BondFilterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * (内部)债券筛选
 *
 * <AUTHOR>
 */
@Api(tags = "(内部)债券筛选-可以刷债券筛选")
@RestController
@RequestMapping("internal/bond/filters")
public class InternalBondFilterController {

    @Resource
    private BondFilterService bondFilterService;

    @ApiOperation("同步债券筛选")
    @PostMapping("sync")
    public void syncBondFilters(@ApiParam("上次同步时间") @RequestParam(required = false) Long lastTimeStamp,
                                     @ApiParam("是否全量删除数据 1:删除 0:不删除") @RequestParam(required = false) Integer deleted) {
         bondFilterService.syncBondFilters(lastTimeStamp, deleted);
    }

    @ApiOperation("同步债券筛选-批量-债券")
    @PostMapping("sync/batch/bond-uni-code")
    public int syncMutiBondUniCodeBondFilters(@RequestBody Long... bondUniCodes) {
        return bondFilterService.syncMutiBondUniCodeBondFilters(bondUniCodes);
    }


    @ApiOperation("同步债券筛选-批量-主体")
    @PostMapping("sync/batch/com-uni-code")
    public int syncMutiComUniCodeBondFilters(@RequestBody Long... comUniCodes) {
        return bondFilterService.syncMutiComUniCodeBondFilters(comUniCodes);
    }

}
