package com.innodealing.onshore.bondservice.service.processor;

import com.google.common.collect.Lists;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondAmountDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.BondCashFlowViewDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.OnshoreBondInfoDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.request.BondOptionRequestDTO;
import com.innodealing.onshore.bondmetadata.enums.CrossMarket;
import com.innodealing.onshore.bondmetadata.enums.Deleted;
import com.innodealing.onshore.bondservice.dao.bond.OnshoreBondFilterDAO;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.service.internal.BondInfoService;
import com.innodealing.onshore.bondservice.service.internal.DwsBondInfoServiceHttpService;
import com.innodealing.onshore.bondservice.service.processor.context.BondProcessContext;
import com.innodealing.onshore.bondservice.service.processor.factory.BondCashDetailCalculatorFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.FIVE_HUNDRED;

/**
 * 债券现金流明细处理器
 * 使用模板方法模式定义处理流程
 *
 * <AUTHOR>
 */
@Component
public class BondCashDetailProcessor {
    @Resource
    private OnshoreBondFilterDAO onshoreBondFilterDAO;
    @Resource
    private DwsBondInfoServiceHttpService dwsBondInfoServiceHttpService;
    @Resource
    private BondInfoService bondInfoService;
    @Resource
    private BondCashDetailCalculatorFactory bondCashDetailCalculatorFactory;



    /**
     * 处理单个主体编码的债券现金流明细
     * 模板方法模式：定义处理流程
     */
    public List<OnshoreBondCashDetailBO> processSingleComUniCode(Long comUniCode) {
        // 1. 准备上下文数据
        BondProcessContext context = this.prepareContext(comUniCode);
        // 3. 处理每个债券
        for (Long bondUniCode : context.getBondUniCodes()) {
            bondCashDetailCalculatorFactory.getBondCashDetailCalculatorChain().calculate(context, bondUniCode);
        }
        return context.getResults();
    }

    /**
     * 处理单个主体编码的债券现金流明细
     * 模板方法模式：定义处理流程
     */
    public List<OnshoreBondCashDetailBO> processSingleComUniCodeAndBondUniCode(Long comUniCode, Long bondUniCode) {
        // 1. 准备上下文数据
        BondProcessContext context = this.prepareContext(comUniCode);
        // 3. 处理每个债券
        bondCashDetailCalculatorFactory.getBondCashDetailCalculatorChain().calculate(context, bondUniCode);
        return context.getResults();
    }

    /**
     * 准备处理上下文
     */
    private BondProcessContext prepareContext(Long comUniCode) {
        BondProcessContext context = new BondProcessContext();
        context.setComUniCode(comUniCode);
        // 获取该主体下所有去重跨市场债券
        List<Long> bondUniCodes = onshoreBondFilterDAO.listCrossMarketDedupStatusBondUniCodes(comUniCode, CrossMarket.CROSS_MARKET.getValue());
        context.setBondUniCodes(bondUniCodes);
        context.setOnshoreBondInfoDTOMap(bondInfoService.listBondInfos(bondUniCodes).stream()
                .collect(Collectors.toMap(OnshoreBondInfoDTO::getBondUniCode, v -> v, (v1, v2) -> v1)));
        // 批量获取基础数据
        context.setBondAmountMap(this.collectBondAmountMap(bondUniCodes));
        // 获取债券含权状态信息
        context.setBondOptionStatusMap(this.collectBondOptionStatusMap(bondUniCodes,context));
        // 获取债券现金流信息
        context.setBondCashFlowMap(this.collectOnshoreBondCashFlowMap(bondUniCodes));
        return context;
    }

    private Map<Long, List<BondAmountDTO>> collectBondAmountMap(List<Long> bondUniCodes) {
        return Lists.partition(bondUniCodes, 500).stream()
                .map(partition -> dwsBondInfoServiceHttpService.listBondAmountDTOs(partition))
                .flatMap(Collection::stream)
                .filter(bondAmount -> Objects.equals(bondAmount.getDeleted(), Deleted.NO_DELETED.getValue()))
                .collect(Collectors.groupingBy(BondAmountDTO::getBondUniCode));
    }

    private Map<Long, BondOptionStatusDTO> collectBondOptionStatusMap(List<Long> bondUniCodes, BondProcessContext context) {
        Map<Long, OnshoreBondInfoDTO> onshoreBondInfoMap = context.getOnshoreBondInfoDTOMap();
        // 原有逻辑保持不变
        return bondUniCodes.parallelStream()
                .map(bondUniCode -> {
                    BondOptionRequestDTO requestDTO = new BondOptionRequestDTO();
                    requestDTO.setBondUniCodes(Collections.singleton(bondUniCode));
                    requestDTO.setStartExerciseDate(Date.valueOf(LocalDate.now()));
                    Optional.ofNullable(onshoreBondInfoMap.get(bondUniCode)).map(OnshoreBondInfoDTO::getMaturityDate).ifPresent(requestDTO::setEndExerciseDate);
                    return dwsBondInfoServiceHttpService.listBondOptionStatusByBondUniCodes(requestDTO);
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(BondOptionStatusDTO::getBondUniCode, Function.identity(), (v1, v2) -> v2));
    }

    private Map<Long, List<BondCashFlowViewDTO>> collectOnshoreBondCashFlowMap(List<Long> bondUniCodes) {
        return Lists.partition(bondUniCodes, FIVE_HUNDRED).stream()
                .map(partition -> dwsBondInfoServiceHttpService.listBondCashFlowViewDTOs(partition))
                .flatMap(Collection::stream)
                .collect(Collectors.groupingBy(BondCashFlowViewDTO::getBondUniCode));
    }
}
