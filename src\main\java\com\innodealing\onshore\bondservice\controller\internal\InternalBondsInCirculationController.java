package com.innodealing.onshore.bondservice.controller.internal;

import com.innodealing.onshore.bondservice.model.dto.request.BondBalanceStatisticsRequestDTO;
import com.innodealing.onshore.bondservice.model.dto.request.BondCashDistributionHistogramRequestDTO;
import com.innodealing.onshore.bondservice.model.dto.response.BondBalanceStatisticsResponseDTO;
import com.innodealing.onshore.bondservice.model.dto.response.BondCashDistributionHistogramResponseDTO;
import com.innodealing.onshore.bondservice.service.BondsInCirculationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流通中债券重构-api
 *
 * <AUTHOR>
 */
@Api(tags = "(内部)重构流通中债券")
@RestController
@RequestMapping("/internal/bond/circulation")
public class InternalBondsInCirculationController {

    @Resource
    private BondsInCirculationService bondsInCirculationService;


    @ApiOperation("规模分布柱状图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondCashDistributionHistogramRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/histogram")
    public BondCashDistributionHistogramResponseDTO bondCashDistributionHistogram(@RequestBody BondCashDistributionHistogramRequestDTO requestBody) {
        return bondsInCirculationService.bondCashDistributionHistogram(requestBody);
    }

    @ApiOperation("债券明细统计")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "requestBody", required = true,
                    dataType = "BondBalanceStatisticsRequestDTO", paramType = "body")
    })
    @PostMapping(value = "/bond/balance/statistics")
    public List<BondBalanceStatisticsResponseDTO> listBondBalanceStatistics(@RequestBody BondBalanceStatisticsRequestDTO requestBody) {
        return bondsInCirculationService.listBondBalanceStatistics(requestBody);
    }
}
