package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComBasicDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComBondDetailDTO;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComRelatedPartyDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "dws-party-info-service", url = "${dws.party.info.url}", path = "/internal")
public interface DwsPartyInfoServiceHttpService {

    @ApiOperation(value = "- 根据数据更新时间和数据最大id增量获取主体信息")
    @PostMapping("/com-basic/listIncrementalComInfo")
    List<ComBasicDTO> listComBasicDTOs(@ApiParam(value = "更新时间") @RequestParam("updateTime") Timestamp updateTime,
                                       @ApiParam(value = "起始id") @RequestParam("startId") Long startId,
                                       @ApiParam(value = "获取数量") @RequestParam(value = "fetchSize", defaultValue = "500") Integer fetchSize);


    @ApiOperation(value = "根据主体唯一编码获取相同实控人的主体信息(comUniCodes不超过500)")
    @PostMapping("/com-related-party/listComRelatedPartyDTOs")
    List<ComRelatedPartyDTO> listComRelatedPartyDTOs(@RequestBody Collection<Long> comUniCodes);


    @ApiOperation(value = "根据主体唯一编码批量获取债券的基础信息(comUniCodes不超过500)")
    @PostMapping("/bond-party/listComBondDetailDTOs")
    List<ComBondDetailDTO> listComBondDetailDTOs(@RequestBody Collection<Long> comUniCodes);

    /**
     * 根据主体唯一编码获取相同实控人的主体唯一编码
     *
     * @param comUniCode 主体唯一编码
     * @return {@link List }<{@link Long }>
     */
    default List<Long> listRelatedPartyComUniCodes(Long comUniCode) {
        return this.listComRelatedPartyDTOs(Collections.singleton(comUniCode)).stream()
                .map(ComRelatedPartyDTO::getRelaComUniCodes)
                .flatMap(Collection::stream)
                .distinct().collect(Collectors.toList());
    }
}
