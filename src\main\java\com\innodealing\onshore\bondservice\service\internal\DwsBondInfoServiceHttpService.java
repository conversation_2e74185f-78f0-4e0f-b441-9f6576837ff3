package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.*;
import com.innodealing.onshore.bondmetadata.dto.dmdataproduct.BondBasicDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondIssueDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondOptionStatusDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.BondTypeDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.request.BondIssueRequestDTO;
import com.innodealing.onshore.bondmetadata.dto.dwsbondinfo.request.BondOptionRequestDTO;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "dws-bond-info-service", url = "${dws.bond.info.url}", path = "/internal")
public interface DwsBondInfoServiceHttpService {

    @ApiOperation(value = "获取债券基础信息")
    @PostMapping("/bond-info/listOnshoreBondBasicInfo")
    List<BondBasicDTO> listOnshoreBondBasicInfo(@RequestBody Long[] bondUniCodes);

    @ApiOperation(value = "获取债券类型信息")
    @PostMapping("/bond-info/bond-types")
    List<BondTypeDTO> getBondTypeByBondUniCodes(@RequestBody Collection<Long> bondUniCodes);

    @ApiOperation(value = "根据债券唯一编码获取债券规模变动信息(bondUniCodes不超过500)")
    @PostMapping("/bond-amount/listOnshoreBondAmountChange")
    List<BondAmountDTO> listBondAmountDTOs(@RequestBody List<Long> bondUniCodes);

    @ApiOperation(value = "根据债券唯一编码获取境内债担保信息(bondUniCodes不超过500)")
    @PostMapping("/bond-guarantee/listOnshoreBondGuarantee")
    List<BondGuaranteeDTO> listBondGuaranteeDTOs(@RequestBody List<Long> bondUniCodes);

    @ApiOperation(value = "根据债券唯一编码获取债券发行的发行中介信息(bondUniCodes不超过500)")
    @PostMapping("/bond-issue-agency/listOnshoreBondIssueAgency")
    List<BondIssueAgencyInfoDTO> listBondIssueAgencyInfoDTOs(@RequestBody List<Long> bondUniCodes);

    @ApiOperation(value = "获取债券发行信息")
    @PostMapping("/bond-info/bond-issues")
     List<BondIssueDTO> listBondIssueByBondUniCodes(@RequestBody BondIssueRequestDTO requestDTO);

    @ApiOperation(value = "债券现金流集合(bondUniCodes不超过500)")
    @PostMapping("/bond-cash-flow/listBondCashFlow")
    List<BondCashFlowViewDTO> listBondCashFlowViewDTOs(@RequestBody List<Long> bondUniCodes);

    @ApiOperation(value = "获取债券某一日的票面利率(bondUniCodes不超过500)")
    @PostMapping("/bond-coupon/listBondCoupons")
    List<BondCouponDTO> listBondCouponDTOs(@RequestBody List<Long> bondUniCodes,
                                           @ApiParam(value = "利率日期") @RequestParam(value = "couponDate", required = false) Date couponDate);

    @ApiOperation(value = "获取债券含权状态信息")
    @PostMapping("/bond-info/bond-option-status")
    List<BondOptionStatusDTO> listBondOptionStatusByBondUniCodes(@RequestBody BondOptionRequestDTO requestDTO);


    @ApiOperation(value = "根据数据更新时间和数据最大id增量获取债券现金流数据")
    @PostMapping("/bond-cash-flow/listIncrementalOnshoreBondCashFlow")
    List<BondCashFlowIncrementDTO> listBondCashFlowIncrementDTOs(@ApiParam(value = "更新时间") @RequestParam("updateTime") Timestamp updateTime,
                                                                 @ApiParam(value = "起始id") @RequestParam("startId") Long startId,
                                                                 @ApiParam(value = "获取数量") @RequestParam(value = "fetchSize", defaultValue = "500") Integer fetchSize);

    @ApiOperation(value = "增量获取债券规模变动信息")
    @PostMapping("/bond-amount/listIncrementalOnshoreBondAmountChange")
    List<BondAmountDTO> listBondAmountDTOs(@ApiParam(value = "更新时间") @RequestParam("updateTime") Timestamp updateTime,
                                           @ApiParam(value = "起始id") @RequestParam("startId") Long startId,
                                           @ApiParam(value = "获取数量") @RequestParam(value = "fetchSize", defaultValue = "500") Integer fetchSize);


}
