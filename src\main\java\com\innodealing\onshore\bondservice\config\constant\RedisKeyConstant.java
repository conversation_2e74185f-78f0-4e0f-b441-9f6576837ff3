package com.innodealing.onshore.bondservice.config.constant;

/**
 * redis key常量
 *
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2025年01月20日 18:29:00
 */
public final class RedisKeyConstant {

    private RedisKeyConstant() {}

    public static final String PREFIX = "onshore-bond-service:";

    /**
     * 流通中债券主体信息
     */
    public static final String REDIS_KEY_COM_INFO_F9_KEY = "onshore-bond-service:com_info:f9:%s";
    /**
     * 流通中债券主体信息
     */
    public static final String REDIS_KEY_RELATED_PARTY_COM_UNI_CODE_KEY = "onshore-bond-service:related_party_com_uni_code:%s";
    public static final String REDIS_KEY_ABS_BOND_UNI_CODE_KEY = "onshore-bond-service:abs_bond_uni_code:%s";
    /**
     * 跳过位置状态
     */
    public static final String REDIS_KEY_MQ_MESSAGE_SKIP_STATUS = PREFIX + "mq_message_skip_status";

    /**
     * 同步计算债券现金流水明细 lock key
     * %s: com_uni_code
     */
    public static final String SYNC_BOND_CASH_DETAIL_LOCK_KEY = PREFIX + "sync_bond_cash_detail:lock:%s";

}
