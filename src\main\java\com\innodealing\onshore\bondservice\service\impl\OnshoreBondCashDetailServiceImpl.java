package com.innodealing.onshore.bondservice.service.impl;

import com.google.common.collect.Lists;
import com.innodealing.commons.object.BeanCopyUtils;
import com.innodealing.onshore.bondmetadata.dto.bondbasic.*;
import com.innodealing.onshore.bondservice.dao.bond.OnshoreBondFilterDAO;
import com.innodealing.onshore.bondservice.dao.pgbond.PgOnshoreBondCashDetailDAO;
import com.innodealing.onshore.bondservice.dao.pgbond.mv.MvOnshoreBondCashDetailMonthDAO;
import com.innodealing.onshore.bondservice.dao.pgbond.mv.MvOnshoreBondCashDetailQuarterDAO;
import com.innodealing.onshore.bondservice.dao.pgbond.mv.MvOnshoreBondCashDetailYearDAO;
import com.innodealing.onshore.bondservice.model.bo.OnshoreBondCashDetailBO;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.PgOnshoreBondCashDetailDO;
import com.innodealing.onshore.bondservice.service.OnshoreBondCashDetailService;
import com.innodealing.onshore.bondservice.service.internal.BondInfoService;
import com.innodealing.onshore.bondservice.service.internal.DwsBondInfoServiceHttpService;
import com.innodealing.onshore.bondservice.service.internal.DwsPartyInfoServiceHttpService;
import com.innodealing.onshore.bondservice.service.internal.InternalKeyValueService;
import com.innodealing.onshore.bondservice.service.processor.BondCashDetailProcessor;
import com.innodealing.onshore.bondservice.utils.RedisLockUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.FIVE_HUNDRED;
import static com.innodealing.onshore.bondservice.config.constant.NumberConstant.ONE_THOUSAND;
import static com.innodealing.onshore.bondservice.config.constant.RedisKeyConstant.SYNC_BOND_CASH_DETAIL_LOCK_KEY;
import static jodd.util.StringPool.COLON;


/**
 * 国内债券现金流水明细表表Service层 {@link PgOnshoreBondCashDetailDO}
 *
 * <AUTHOR>
 */
@Service
public class OnshoreBondCashDetailServiceImpl implements OnshoreBondCashDetailService {
    private static final int LIMIT = 500;
    private static final Logger log = LoggerFactory.getLogger(OnshoreBondCashDetailServiceImpl.class);
    @Resource
    private BondCashDetailProcessor bondCashDetailProcessor;
    @Resource
    private OnshoreBondFilterDAO onshoreBondFilterDAO;
    @Resource
    private PgOnshoreBondCashDetailDAO pgOnshoreBondCashDetailDAO;
    @Resource
    private MvOnshoreBondCashDetailMonthDAO mvOnshoreBondCashDetailMonthDAO;
    @Resource
    private MvOnshoreBondCashDetailQuarterDAO mvOnshoreBondCashDetailQuarterDAO;
    @Resource
    private MvOnshoreBondCashDetailYearDAO mvOnshoreBondCashDetailYearDAO;

    @Resource
    private DwsPartyInfoServiceHttpService dwsPartyInfoServiceHttpService;
    @Resource
    private BondInfoService bondInfoService;
    @Resource
    private InternalKeyValueService internalKeyValueService;
    @Resource
    private DwsBondInfoServiceHttpService dwsBondInfoServiceHttpService;
    @Resource
    private RedisLockUtils redisLockUtils;
    /**
     * 获取增量onshore_bond_filter数据键
     * value: %s:%s
     * %s bondUniCode
     * %s timestamp
     */
    private static final String SYNC_ONSHORE_BOND_FILTER_KEY = "onshore-bond-service:sync:increment:onshore_bond_filter";
    /**
     * 获取增量bond_amount数据键
     * value: %s:%s
     * %s id
     * %s timestamp
     */
    private static final String SYNC_BOND_AMOUNT_KEY = "onshore-bond-service:sync:increment:bond_amount";
    /**
     * 获取增量bond_cash_flow数据键
     * value: %s:%s
     * %s id
     * %s timestamp
     */
    private static final String SYNC_BOND_CASH_FLOW_KEY = "onshore-bond-service:sync:increment:bond_cash_flow";
    /**
     * 获取增量com_info数据键
     * value: %s:%s
     * %s id
     * %s timestamp
     */
    private static final String SYNC_PARTY_INFO_KEY = "onshore-bond-service:sync:increment:party_info";

    /**
     * 函数式接口：三参数函数
     */
    @FunctionalInterface
    private interface TriFunction<T, U, V, R> {
        R apply(T t, U u, V v);
    }


    @Override
    public void syncOnshoreBondCashDetail(Set<Long> comUniCodes) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("[syncOnshoreBondCashDetail] started | inputComUniCodes={}",
                CollectionUtils.isNotEmpty(comUniCodes) ? comUniCodes.size() : "all");
        Set<Long> comUniCodeSet = CollectionUtils.isNotEmpty(comUniCodes) ? comUniCodes : listAllComUniCodes();
        comUniCodeSet.parallelStream().forEach(this::handleSingleComUniCode);
        stopWatch.stop();
        log.info("[syncOnshoreBondCashDetail] completed | duration={}s | processedComUniCodes={}",
                stopWatch.getTotalTimeSeconds(), comUniCodeSet.size());
    }

    @Override
    public void handleSingleOnshoreBondCashDetail(Long comUniCode, Long bondUniCode) {
        redisLockUtils.lock(String.format(SYNC_BOND_CASH_DETAIL_LOCK_KEY, comUniCode + "_" + bondUniCode), Strings.EMPTY, () -> {
            List<OnshoreBondCashDetailBO> process = bondCashDetailProcessor.processSingleComUniCodeAndBondUniCode(comUniCode, bondUniCode);
            Lists.partition(process, ONE_THOUSAND.intValue()).forEach(v -> {
                pgOnshoreBondCashDetailDAO.batchUpsert(BeanCopyUtils.copyList(v, PgOnshoreBondCashDetailDO.class));
            });
            return Strings.EMPTY;
        });
    }

    @Override
    public void jobSyncOnshoreBondCashDetail() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        log.info("[jobSyncOnshoreBondCashDetail] started");
        Set<Long> bondUniCodes = new CopyOnWriteArraySet<>();
        Optional<OnshoreBondFilterDO> maxOnshoreBondFilterOpt = onshoreBondFilterDAO.getMaxOnshoreBondFilterDO();
        // 1. onshore_bond_filter
        // 获取 onshore_bond_filter 增量数据的 bondUniCode
        Set<Long> filterBondUniCodes = this.collectIncrementOnshoreBondFilterBondUniCodes(maxOnshoreBondFilterOpt);
        bondUniCodes.addAll(filterBondUniCodes);
        log.info("[jobSyncOnshoreBondCashDetail] collected onshore_bond_filter increments | count={}", filterBondUniCodes.size());
        // 2. bond_amount
        Set<Long> amountBondUniCodes = this.collectIncrementOnshoreBondAmountChangeBondUniCodes();
        bondUniCodes.addAll(amountBondUniCodes);
        log.info("[jobSyncOnshoreBondCashDetail] collected bond_amount increments | count={}", amountBondUniCodes.size());
        // 3.bond_cash_flow
        Set<Long> cashFlowBondUniCodes = this.collectIncrementOnshoreBondCashFlowBondUniCodes();
        bondUniCodes.addAll(cashFlowBondUniCodes);
        log.info("[jobSyncOnshoreBondCashDetail] collected bond_cash_flow increments | count={}", cashFlowBondUniCodes.size());
        log.info("[jobSyncOnshoreBondCashDetail] total incremental bonds collected | totalBondUniCodes={}", bondUniCodes.size());
        Map<Long/*bondUniCode*/, Long/*comUniCode*/> syncMap = Lists.partition(new ArrayList<>(bondUniCodes), FIVE_HUNDRED).stream()
                .map(v -> bondInfoService.listBondInfos(v))
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(OnshoreBondInfoDTO::getBondUniCode, OnshoreBondInfoDTO::getComUniCode, (v1, v2) -> v1));
        log.info("[jobSyncOnshoreBondCashDetail] bond info mapping completed | mappedBonds={}", syncMap.size());
        syncMap.forEach((k, v) -> handleSingleOnshoreBondCashDetail(v, k));
        stopWatch.stop();
        log.info("[jobSyncOnshoreBondCashDetail] completed | duration={}s | processedBonds.size={}",
                stopWatch.getTotalTimeSeconds(), syncMap.size());
    }

    @Override
    public void refreshMaterializedView() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("刷新债券现金流水物化视图-[Month]");
        log.info("refreshAreaMaturityMaterializedView start");
        mvOnshoreBondCashDetailMonthDAO.aliasRefreshView();
        stopWatch.stop();
        log.info("刷新债券现金流水物化视图-[Month] end,cost:{}", stopWatch.getTotalTimeMillis());

        stopWatch.start("刷新债券现金流水物化视图-[Quarter]");
        mvOnshoreBondCashDetailQuarterDAO.aliasRefreshView();
        stopWatch.stop();
        log.info("刷新债券现金流水物化视图-[Quarter] end,cost:{}", stopWatch.getTotalTimeMillis());

        stopWatch.start("刷新债券现金流水物化视图-[Year]");
        mvOnshoreBondCashDetailYearDAO.aliasRefreshView();
        stopWatch.stop();
        log.info("刷新债券现金流水物化视图-[Year] end,cost:{}", stopWatch.getTotalTimeMillis());
    }

    @Override
    public void deleteAllOnshoreBondCashDetail() {
        pgOnshoreBondCashDetailDAO.deleteAll();
    }

    private Set<Long> collectIncrementOnshoreBondCashFlowBondUniCodes() {
        return collectIncrementalBondUniCodes(
                SYNC_BOND_CASH_FLOW_KEY,
                dwsBondInfoServiceHttpService::listBondCashFlowIncrementDTOs,
                BondCashFlowIncrementDTO::getBondUniCode,
                BondCashFlowIncrementDTO::getId,
                BondCashFlowIncrementDTO::getUpdateTime,
                () -> 0L
        );
    }

    private Set<Long> collectIncrementOnshoreBondAmountChangeBondUniCodes() {
        return collectIncrementalBondUniCodes(
                SYNC_BOND_AMOUNT_KEY,
                dwsBondInfoServiceHttpService::listBondAmountDTOs,
                BondAmountDTO::getBondUniCode,
                BondAmountDTO::getId,
                BondAmountDTO::getUpdateTime,
                () -> 0L
        );
    }

    private Set<Long> collectIncrementOnshoreBondFilterBondUniCodes(Optional<OnshoreBondFilterDO> maxOnshoreBondFilterOpt) {
        return collectIncrementalBondUniCodes(
                SYNC_ONSHORE_BOND_FILTER_KEY,
                (lastUpdateTime, lastBondUniCode, limit) ->
                        bondInfoService.listBondFilters(null, lastUpdateTime, lastBondUniCode, limit),
                OnshoreBondFilterV3DTO::getBondUniCode,
                OnshoreBondFilterV3DTO::getBondUniCode,
                OnshoreBondFilterV3DTO::getUpdateTime,
                () -> maxOnshoreBondFilterOpt.isPresent() ? maxOnshoreBondFilterOpt.get().getBondUniCode() : 0L
        );
    }

    /**
     * 通用的增量数据收集方法
     *
     * @param redisKey              Redis键
     * @param fetcher               数据获取函数
     * @param bondUniCodeExtractor  bondUniCode提取函数
     * @param updateTimeExtractor   更新时间提取函数
     * @param defaultLastIdSupplier 默认lastId提供者
     * @param <T>                   数据类型
     * @return bondUniCode集合
     */
    private <T> Set<Long> collectIncrementalBondUniCodes(
            String redisKey,
            TriFunction<Timestamp, Long, Integer, List<T>> fetcher,
            Function<T, Long> bondUniCodeExtractor,
            Function<T, Long> lastCodeExtractor,
            Function<T, Timestamp> updateTimeExtractor,
            Supplier<Long> defaultLastIdSupplier) {
        Set<Long> bondUniCodes = new HashSet<>();
        String incrementVal = internalKeyValueService.getValue(redisKey);
        // 先从缓存记录服务获取信息，获取不到使用默认值
        Timestamp lastUpdateTime = Optional.ofNullable(incrementVal).map(v -> v.split(COLON)[1]).map(Long::parseLong).map(Timestamp::new)
                .orElseGet(() -> Timestamp.valueOf(LocalDateTime.now()));
        Long lastId = Optional.ofNullable(incrementVal).map(v -> v.split(COLON)[0]).map(Long::parseLong)
                .orElseGet(defaultLastIdSupplier);
        Integer fetchSize = null;
        Long currentLastId = defaultLastIdSupplier.get();
        while (Objects.isNull(fetchSize) || fetchSize == LIMIT) {
            List<T> dataList = fetcher.apply(lastUpdateTime, currentLastId, LIMIT);
            Long currentLastId1 = dataList.stream().map(lastCodeExtractor).max(Long::compareTo).orElse(currentLastId);
            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }
            bondUniCodes.addAll(dataList.stream().map(bondUniCodeExtractor).collect(Collectors.toSet()));
            lastUpdateTime = dataList.stream().map(updateTimeExtractor).max(Timestamp::compareTo).orElse(Timestamp.valueOf(LocalDateTime.now()));
            // 更新lastId为最新的
            currentLastId = currentLastId1;
            fetchSize = dataList.size();
            internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(
                    redisKey, String.format("%s:%s", currentLastId, lastUpdateTime.getTime())));
        }
        return bondUniCodes;
    }

    private void handleSingleComUniCode(Long comUniCode) {
        redisLockUtils.lock(String.format(SYNC_BOND_CASH_DETAIL_LOCK_KEY, comUniCode), "sync failed: " + comUniCode, () -> {
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            log.info("[handleSingleComUniCode] processing started | comUniCode={}", comUniCode);
            List<OnshoreBondCashDetailBO> process = bondCashDetailProcessor.processSingleComUniCode(comUniCode);
            // 大数量in导致底层框架cpu飚高，拆分
            int totalRecords = process.size();
            Lists.partition(process, ONE_THOUSAND.intValue()).forEach(v -> {
                //onshoreBondCashDetailDAO.batchUpsert(v);
                pgOnshoreBondCashDetailDAO.batchUpsert(BeanCopyUtils.copyList(v, PgOnshoreBondCashDetailDO.class));
            });
            stopWatch.stop();
            log.info("[handleSingleComUniCode] completed | comUniCode={} | duration={}s | recordsProcessed.size={}",
                    comUniCode, stopWatch.getTotalTimeSeconds(), totalRecords);
            return "sync completed for comUniCode: " + comUniCode;
        });
    }

    private Set<Long> listAllComUniCodes() {
        internalKeyValueService.saveKeyValue(new InternalKeyValueRequestDTO(SYNC_PARTY_INFO_KEY, "0:0"));
        return collectIncrementalBondUniCodes(
                SYNC_PARTY_INFO_KEY,
                (lastUpdateTime, lastBondUniCode, limit) ->
                        dwsPartyInfoServiceHttpService.listComBasicDTOs(lastUpdateTime, lastBondUniCode, limit),
                ComBasicDTO::getComUniCode,
                ComBasicDTO::getId,
                ComBasicDTO::getUpdateTime,
                () -> 0L
        );
    }
}
