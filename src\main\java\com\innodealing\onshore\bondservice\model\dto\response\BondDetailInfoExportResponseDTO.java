package com.innodealing.onshore.bondservice.model.dto.response;


import com.alibaba.excel.annotation.ExcelProperty;
import com.innodealing.onshore.bondservice.excel.conveter.DateDateFormat10Converter;
import com.innodealing.onshore.bondservice.excel.conveter.Number2ScaleConverter;
import com.innodealing.onshore.bondservice.excel.conveter.Number4ScaleConverter;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondDetailInfoExportResponseDTO {
    @ApiModelProperty("募集: 1: 公募; 0: 私募")
    @ExcelProperty(value = "募集方式")
    private String publicOffering;
    @ApiModelProperty("债券简称")
    @ExcelProperty(value = "债券简称")
    private String bondShortName;
    @ApiModelProperty("债券代码（外部）")
    @ExcelProperty(value = "债券代码")
    private String bondCode;
    @ApiModelProperty("主体全称")
    @ExcelProperty(value = "发行人")
    private String comFullName;
    @ApiModelProperty("债券类型")
    @ExcelProperty(value = "债券类型")
    private String displayBondType;
    @ApiModelProperty("发行日")
    @ExcelProperty(value = "发行日",converter = DateDateFormat10Converter.class)
    private Date issueStartDate;
    @ApiModelProperty("发行价")
    @ExcelProperty(value = "发行价",converter = Number2ScaleConverter.class)
    private BigDecimal issuePrice;
    @ApiModelProperty("上市日期")
    @ExcelProperty(value = "上市日",converter = DateDateFormat10Converter.class)
    private Date listDate;
    @ApiModelProperty("交易场所  1 深圳证券交易所;2 上海证券交易所; 3 银行间市场;4 柜台交易市场; 99 其他")
    @ExcelProperty(value = "交易场所")
    private String secondMarket;
    @ApiModelProperty("剩余期限")
    @ExcelProperty(value = "剩余期限")
    private String remainingTenor;

    @ApiModelProperty("债券余额 (亿)")
    @ExcelProperty(value = "债券余额(亿)",converter = Number4ScaleConverter.class)
    private BigDecimal bondBalance;
    @ApiModelProperty("到期日期")
    @ExcelProperty(value = "到期日",converter = DateDateFormat10Converter.class)
    private Date maturityDate;
    @ApiModelProperty("下一行权日")
    @ExcelProperty(value = "下一行权日")
    private String exerciseDate;
    @ApiModelProperty("最新票面利率(%)，优先取t_bond_basic_info表new_coup_rate，若该字段为空或为0，取ref_yield")
    @ExcelProperty(value = "最新票面利率",converter = Number4ScaleConverter.class)
    private BigDecimal latestCouponRate;
    @ApiModelProperty("主承销商")
    @ExcelProperty(value = "主承销商")
    private String leadUnderwriter;
    @ApiModelProperty("注册文书号")
    @ExcelProperty(value = "注册文书号")
    private String regNoticeNumber;
    @ApiModelProperty("募集资金用途")
    @ExcelProperty(value = "募集资金用途")
    private String capitalCollectionUsage;
    @ApiModelProperty("担保人")
    @ExcelProperty(value = "担保人 ")
    private String guarantor;
    @ApiModelProperty("原始权益人名称")
    @ExcelProperty(value = "原始权益人名称")
    private String originalEquityHolderName;
    @ApiModelProperty("信用主体")
    @ExcelProperty(value = "信用主体")
    private String creditSubjectName;
    @ApiModelProperty("产品全称")
    @ExcelProperty(value = "产品全称")
    private String absFullName;
    @ApiModelProperty("产品简称")
    @ExcelProperty(value = "产品简称")
    private String absShortName;
    @ApiModelProperty("循环池类型名称")
    @ExcelProperty(value = "循环池类型")
    private String assetStructureTypeName;
    @ApiModelProperty("基础资产分类名称")
    @ExcelProperty(value = "基础资产分类")
    private String productTypeName;
    @ApiModelProperty("法定到期日")
    @ExcelProperty(value = "法定到期日",converter = DateDateFormat10Converter.class)
    private Date legalMaturityDate;

    @ApiModelProperty("主债评级")
    @ExcelProperty(value = "主债评级")
    private String bondExtRatingMapping;
    @ApiModelProperty("中债隐含评级")
    @ExcelProperty(value = "中债隐含评级")
    private String bondImpliedRating;
    @ApiModelProperty("中债行权收益率/中债到期收益率")
    @ExcelProperty(value = "中债估值(行/到)")
    private String cbYte;
    @ApiModelProperty("中证行权收益率/中证到期收益率")
    @ExcelProperty(value = "中证估值(行/到)")
    private String csYte;


    public String getPublicOffering() {
        return publicOffering;
    }

    public void setPublicOffering(String publicOffering) {
        this.publicOffering = publicOffering;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getComFullName() {
        return comFullName;
    }

    public void setComFullName(String comFullName) {
        this.comFullName = comFullName;
    }

    public String getDisplayBondType() {
        return displayBondType;
    }

    public void setDisplayBondType(String displayBondType) {
        this.displayBondType = displayBondType;
    }

    public Date getIssueStartDate() {
        return issueStartDate;
    }

    public void setIssueStartDate(Date issueStartDate) {
        this.issueStartDate = issueStartDate;
    }

    public BigDecimal getIssuePrice() {
        return issuePrice;
    }

    public void setIssuePrice(BigDecimal issuePrice) {
        this.issuePrice = issuePrice;
    }

    public Date getListDate() {
        return listDate;
    }

    public void setListDate(Date listDate) {
        this.listDate = listDate;
    }

    public String getSecondMarket() {
        return secondMarket;
    }

    public void setSecondMarket(String secondMarket) {
        this.secondMarket = secondMarket;
    }

    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }

    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }

    public Date getMaturityDate() {
        return maturityDate;
    }

    public void setMaturityDate(Date maturityDate) {
        this.maturityDate = maturityDate;
    }

    public String getExerciseDate() {
        return exerciseDate;
    }

    public void setExerciseDate(String exerciseDate) {
        this.exerciseDate = exerciseDate;
    }

    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }

    public String getLeadUnderwriter() {
        return leadUnderwriter;
    }

    public void setLeadUnderwriter(String leadUnderwriter) {
        this.leadUnderwriter = leadUnderwriter;
    }

    public String getRegNoticeNumber() {
        return regNoticeNumber;
    }

    public void setRegNoticeNumber(String regNoticeNumber) {
        this.regNoticeNumber = regNoticeNumber;
    }

    public String getCapitalCollectionUsage() {
        return capitalCollectionUsage;
    }

    public void setCapitalCollectionUsage(String capitalCollectionUsage) {
        this.capitalCollectionUsage = capitalCollectionUsage;
    }

    public String getGuarantor() {
        return guarantor;
    }

    public void setGuarantor(String guarantor) {
        this.guarantor = guarantor;
    }

    public String getOriginalEquityHolderName() {
        return originalEquityHolderName;
    }

    public void setOriginalEquityHolderName(String originalEquityHolderName) {
        this.originalEquityHolderName = originalEquityHolderName;
    }

    public String getCreditSubjectName() {
        return creditSubjectName;
    }

    public void setCreditSubjectName(String creditSubjectName) {
        this.creditSubjectName = creditSubjectName;
    }

    public String getAbsFullName() {
        return absFullName;
    }

    public void setAbsFullName(String absFullName) {
        this.absFullName = absFullName;
    }

    public String getAbsShortName() {
        return absShortName;
    }

    public void setAbsShortName(String absShortName) {
        this.absShortName = absShortName;
    }

    public String getAssetStructureTypeName() {
        return assetStructureTypeName;
    }

    public void setAssetStructureTypeName(String assetStructureTypeName) {
        this.assetStructureTypeName = assetStructureTypeName;
    }

    public String getProductTypeName() {
        return productTypeName;
    }

    public void setProductTypeName(String productTypeName) {
        this.productTypeName = productTypeName;
    }

    public Date getLegalMaturityDate() {
        return legalMaturityDate;
    }

    public void setLegalMaturityDate(Date legalMaturityDate) {
        this.legalMaturityDate = legalMaturityDate;
    }

    public String getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(String bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }

    public String getBondImpliedRating() {
        return bondImpliedRating;
    }

    public void setBondImpliedRating(String bondImpliedRating) {
        this.bondImpliedRating = bondImpliedRating;
    }

    public String getCbYte() {
        return cbYte;
    }

    public void setCbYte(String cbYte) {
        this.cbYte = cbYte;
    }

    public String getCsYte() {
        return csYte;
    }

    public void setCsYte(String csYte) {
        this.csYte = csYte;
    }
}
