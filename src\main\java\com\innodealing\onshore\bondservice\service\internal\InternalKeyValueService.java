package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.InternalKeyValueRequestDTO;
import jodd.util.StringUtil;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Optional;

/**
 * 内部Key value 服务
 *
 * <AUTHOR>
 * @date 2020/11/4
 */
@FeignClient(name = "internalKeyValueService", url = "${bond.basic.api.url}", path = "/internal/keyValue")
public interface InternalKeyValueService {


    /**
     * 保存key value
     *
     * @param internalKeyValueRequestDTO 保存key value 键值对
     * @return 影响行数
     */
    @PostMapping
    Integer saveKeyValue(@RequestBody InternalKeyValueRequestDTO internalKeyValueRequestDTO);

    /**
     * 获取 value
     *
     * @param key 键
     * @return value
     */
    @GetMapping("{key}")
    String getValue(@PathVariable String key);

    /**
     * 获取 long类型的value
     *
     * @param key 键
     * @return value
     */
    default Optional<Long> getLongValue(String key) {
        String value = this.getValue(key);
        if (StringUtil.isBlank(value)) {
            return Optional.empty();
        }
        return Optional.of(Long.parseLong(value));
    }

}
