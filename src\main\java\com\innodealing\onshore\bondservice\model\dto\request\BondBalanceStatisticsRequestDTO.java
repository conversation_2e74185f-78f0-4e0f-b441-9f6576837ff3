package com.innodealing.onshore.bondservice.model.dto.request;


import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondBalanceStatisticsRequestDTO {

    @ApiModelProperty("本公司")
    private Long comUniCode;
    @ApiModelProperty("相同实控人编码")
    private List<Long> relationComUniCodes;
    @ApiModelProperty("关联ABS债券唯一编码")
    private List<Long> absBondUniCodes;
    @ApiModelProperty("去重跨市场债 0.否 1.是")
    private Integer crossMarketDedupStatus;
    @ApiModelProperty("全部不传、 3.银行间、2.上交所、1.深交所、78.北交所 默认选中全部")
    private List<Integer> secondMarkets;
    @ApiModelProperty("募集: 1: 公募; 0: 私募")
    private List<Integer> publicOfferings;
    @ApiModelProperty("MarketCalendarLatestProTypeEnum BondIssueStatusEnum全部不传、 17.存续中; 18.已到期;0.发行中;")
    private List<Integer> bondStatus;
    @ApiModelProperty("BondType BondTermTypeEnum 全部不传、 108.永续; 106.回售; 105.赎回;12.票面利率选择权")
    private List<Integer> bondTerms;
    @ApiModelProperty
    private List<Integer> bondTypes;


    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public List<Long> getRelationComUniCodes() {
        return relationComUniCodes;
    }

    public void setRelationComUniCodes(List<Long> relationComUniCodes) {
        this.relationComUniCodes = relationComUniCodes;
    }

    public List<Long> getAbsBondUniCodes() {
        return absBondUniCodes;
    }

    public void setAbsBondUniCodes(List<Long> absBondUniCodes) {
        this.absBondUniCodes = absBondUniCodes;
    }

    public List<Integer> getSecondMarkets() {
        return secondMarkets;
    }

    public void setSecondMarkets(List<Integer> secondMarkets) {
        this.secondMarkets = secondMarkets;
    }

    public List<Integer> getPublicOfferings() {
        return publicOfferings;
    }

    public void setPublicOfferings(List<Integer> publicOfferings) {
        this.publicOfferings = publicOfferings;
    }

    public List<Integer> getBondStatus() {
        return bondStatus;
    }

    public void setBondStatus(List<Integer> bondStatus) {
        this.bondStatus = bondStatus;
    }

    public List<Integer> getBondTerms() {
        return bondTerms;
    }

    public void setBondTerms(List<Integer> bondTerms) {
        this.bondTerms = bondTerms;
    }

    public List<Integer> getBondTypes() {
        return bondTypes;
    }

    public void setBondTypes(List<Integer> bondTypes) {
        this.bondTypes = bondTypes;
    }

    public Integer getCrossMarketDedupStatus() {
        return crossMarketDedupStatus;
    }

    public void setCrossMarketDedupStatus(Integer crossMarketDedupStatus) {
        this.crossMarketDedupStatus = crossMarketDedupStatus;
    }


}
