package com.innodealing.onshore.bondservice.dao.pgbond.mv;

import com.github.wz2cool.dynamic.GroupByQuery;
import com.github.wz2cool.dynamic.GroupedQuery;
import com.github.wz2cool.dynamic.builder.direction.ISortDirection;
import com.innodealing.onshore.bondservice.mapper.pgbond.mv.MvOnshoreBondCashDetailYearMapper;
import com.innodealing.onshore.bondservice.mapper.pgbond.mv.group.MvOnshoreBondCashDetailYearGroupMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailYearGroupDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.mv.MvOnshoreBondCashDetailYearDO;
import com.innodealing.onshore.dao.postgresql.table.PgOperationTemplate;
import com.innodealing.onshore.service.AliasRefreshMaterializedView;
import com.sun.istack.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Objects;

import static com.github.wz2cool.dynamic.builder.DynamicQueryBuilderHelper.asc;

/**
 * 历史-目前只接2023年4季度历史分时数据物化视图
 *
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2024年10月22日 20:43:00
 */
@Repository
public class MvOnshoreBondCashDetailYearDAO extends AliasRefreshMaterializedView {

    @Resource
    private MvOnshoreBondCashDetailYearMapper mvOnshoreBondCashDetailYearMapper;
    @Resource
    private MvOnshoreBondCashDetailYearGroupMapper mvOnshoreBondCashDetailYearGroupMapper;

    /**
     * 实例化
     *
     * @param pgOperationTemplate 模板类
     * @author: 张飞翔
     * @date: 2023/8/21 13:59
     */
    public MvOnshoreBondCashDetailYearDAO(@Autowired PgOperationTemplate pgOperationTemplate) {
        super(MvOnshoreBondCashDetailYearDO.class, pgOperationTemplate);
    }

    /**
     * 国内债券现金流按年统计物化视图
     *
     * @param comUniCodes 发行人编码
     * @return 现金流统计数据
     */

    public List<MvOnshoreBondCashDetailYearGroupDO> listMvOnshoreBondCashDetailYearGroupDO(@NotNull Collection<Long> comUniCodes) {
        GroupedQuery<MvOnshoreBondCashDetailYearDO, MvOnshoreBondCashDetailYearGroupDO> groupedQuery =
                GroupByQuery.createQuery(MvOnshoreBondCashDetailYearDO.class, MvOnshoreBondCashDetailYearGroupDO.class)
                        .and(Objects.nonNull(comUniCodes), MvOnshoreBondCashDetailYearDO::getComUniCode, x -> x.in(comUniCodes))
                        .groupBy(MvOnshoreBondCashDetailYearDO::getDataYear);
        return mvOnshoreBondCashDetailYearGroupMapper.selectByGroupedQuery(groupedQuery);
    }

}
