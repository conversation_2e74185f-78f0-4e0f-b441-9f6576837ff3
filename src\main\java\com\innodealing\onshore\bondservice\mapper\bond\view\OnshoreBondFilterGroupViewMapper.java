package com.innodealing.onshore.bondservice.mapper.bond.view;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView;

/**
 * 基础筛选mapper
 *
 * <AUTHOR>
 */
public interface OnshoreBondFilterGroupViewMapper extends SelectByGroupedQueryMapper<OnshoreBondFilterDO, OnshoreBondFilterView> {

}
