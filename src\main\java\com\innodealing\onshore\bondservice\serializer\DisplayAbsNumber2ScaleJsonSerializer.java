package com.innodealing.onshore.bondservice.serializer;

import com.innodealing.onshore.bondmetadata.json.serializer.DisplayNumberJsonSerializer;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

import static com.innodealing.onshore.bondmetadata.utils.PriceUtils.isNullOrZero;

/**
 * 显示bigDecimal json序列化, 保留两位
 *
 * <AUTHOR>
 * @date 2021/6/29 13:50
 */
public class DisplayAbsNumber2ScaleJsonSerializer extends DisplayNumberJsonSerializer {

    @Override
    public String getDisplayString(final Number number) {
        if (isNullOrZero(number)) {
            return DisplayNumberJsonSerializer.EMPTY_PLACEHOLDER;
        }
        DecimalFormat df = new DecimalFormat("0.00");
        return df.format(new BigDecimal(Objects.toString(number)).abs());
    }

}
