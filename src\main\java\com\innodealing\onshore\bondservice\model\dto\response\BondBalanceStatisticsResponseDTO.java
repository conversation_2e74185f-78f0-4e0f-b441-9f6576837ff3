package com.innodealing.onshore.bondservice.model.dto.response;


import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayNumber2ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondBalanceStatisticsResponseDTO {

    @ApiModelProperty("主体关联类型：1.本公司 2.相同实控人 3.关联ABS")
    private Integer comRelationType;
    @ApiModelProperty("存续只数")
    private Integer durationCount;
    @ApiModelProperty("存续债券余额总计")
    @JsonSerialize(using = DisplayNumber2ScaleJsonSerializer.class, nullsUsing = DisplayNumber2ScaleJsonSerializer.class)
    private BigDecimal durationTotalBondBalance;
    @ApiModelProperty("到期只数")
    private Integer expiredCount;
    @ApiModelProperty("到期债券余额总计")
    @JsonSerialize(using = DisplayNumber2ScaleJsonSerializer.class, nullsUsing = DisplayNumber2ScaleJsonSerializer.class)
    private BigDecimal expiredTotalBondBalance;

    public Integer getComRelationType() {
        return comRelationType;
    }

    public void setComRelationType(Integer comRelationType) {
        this.comRelationType = comRelationType;
    }

    public Integer getDurationCount() {
        return durationCount;
    }

    public void setDurationCount(Integer durationCount) {
        this.durationCount = durationCount;
    }

    public BigDecimal getDurationTotalBondBalance() {
        return durationTotalBondBalance;
    }

    public void setDurationTotalBondBalance(BigDecimal durationTotalBondBalance) {
        this.durationTotalBondBalance = durationTotalBondBalance;
    }

    public Integer getExpiredCount() {
        return expiredCount;
    }

    public void setExpiredCount(Integer expiredCount) {
        this.expiredCount = expiredCount;
    }

    public BigDecimal getExpiredTotalBondBalance() {
        return expiredTotalBondBalance;
    }

    public void setExpiredTotalBondBalance(BigDecimal expiredTotalBondBalance) {
        this.expiredTotalBondBalance = expiredTotalBondBalance;
    }
}
