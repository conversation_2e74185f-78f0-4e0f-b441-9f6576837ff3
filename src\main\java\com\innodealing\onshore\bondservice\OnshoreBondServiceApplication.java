package com.innodealing.onshore.bondservice;

import com.innodealing.onshore.annotation.EnablePostgresqlDefaultDataSourceConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 债券服务启动类
 *
 * <AUTHOR> 2025-06-16
 */
@EnableFeignClients
@EnableSwagger2
@SpringBootApplication
@EnablePostgresqlDefaultDataSourceConfig
public class OnshoreBondServiceApplication {

    static {
        System.setProperty("java.util.concurrent.ForkJoinPool.common.parallelism", "64");
    }

    /**
     * 主方法
     *
     * @param args 参数
     */
    public static void main(String[] args) {
        SpringApplication.run(OnshoreBondServiceApplication.class, args);
    }

}