package com.innodealing.onshore.bondservice.model.entity.bond.view;

import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * 境内债券筛选信息表实体对象
 *
 * <AUTHOR>
 */
@Table(name = "onshore_bond_filter")
public class OnshoreBondFilterView extends OnshoreBondFilterDO {
    /**
     * 存续只数
     */
    @Column(name = "COUNT(CASE WHEN outstanding_status = 1 THEN 1 END)")
    private Integer durationCount;
    /**
     * 存续债券余额
     */
    @Column(name = "SUM(CASE WHEN outstanding_status = 1 THEN bond_balance ELSE 0 END)")
    private BigDecimal durationTotalBondBalance;
    /**
     * 存续债券余额占比
     */
    @Column(name = "COUNT(CASE WHEN expired = 1 THEN 1 END)")
    private Integer expiredCount;
    /**
     * 存续债券余额占比
     **/
    @Column(name = "SUM(CASE WHEN expired = 1 THEN bond_balance ELSE 0 END)")
    private BigDecimal expiredTotalBondBalance;



    public Integer getDurationCount() {
        return durationCount;
    }

    public void setDurationCount(Integer durationCount) {
        this.durationCount = durationCount;
    }

    public BigDecimal getDurationTotalBondBalance() {
        return durationTotalBondBalance;
    }

    public void setDurationTotalBondBalance(BigDecimal durationTotalBondBalance) {
        this.durationTotalBondBalance = durationTotalBondBalance;
    }

    public Integer getExpiredCount() {
        return expiredCount;
    }

    public void setExpiredCount(Integer expiredCount) {
        this.expiredCount = expiredCount;
    }

    public BigDecimal getExpiredTotalBondBalance() {
        return expiredTotalBondBalance;
    }

    public void setExpiredTotalBondBalance(BigDecimal expiredTotalBondBalance) {
        this.expiredTotalBondBalance = expiredTotalBondBalance;
    }
}

