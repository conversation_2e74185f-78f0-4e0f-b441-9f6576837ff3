package com.innodealing.onshore.bondservice.model.dto.request;


import io.swagger.annotations.ApiModelProperty;

/**
 * 流通中债券-规模分布柱状图-请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondDetailInfoPageExportRequestDTO extends BondDetailInfoPageRequestDTO{
    @ApiModelProperty("缓存key")
    private String tableKey;
    @ApiModelProperty("默认列")
    private String defaultColumns;
    @ApiModelProperty("筛选描述")
    private String filterDescriptor;

    public String getTableKey() {
        return tableKey;
    }

    public void setTableKey(String tableKey) {
        this.tableKey = tableKey;
    }

    public String getDefaultColumns() {
        return defaultColumns;
    }

    public void setDefaultColumns(String defaultColumns) {
        this.defaultColumns = defaultColumns;
    }

    public String getFilterDescriptor() {
        return filterDescriptor;
    }

    public void setFilterDescriptor(String filterDescriptor) {
        this.filterDescriptor = filterDescriptor;
    }
}
