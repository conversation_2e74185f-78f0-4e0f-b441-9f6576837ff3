package com.innodealing.onshore.bondservice.model.dto.response;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.innodealing.onshore.bondmetadata.json.serializer.DisplayDateJsonWithFormatSerializer;
import com.innodealing.onshore.bondservice.serializer.AbsTenThousandToYuan2ScaleJsonSerializer;
import io.swagger.annotations.ApiModelProperty;

import java.math.BigDecimal;
import java.sql.Date;

/**
 * 流通中债券-规模分布明细分页响应参数
 *
 * <AUTHOR>
 */
public class BondCashDistributionDetailPageResponseDTO {

    @ApiModelProperty("债券唯一代码")
    private Long bondUniCode;
    @ApiModelProperty("数据日期")
    private Date dataDate;
    @ApiModelProperty("债券code")
    private String bondCode;
    @ApiModelProperty("债券简称")
    private String bondShortName;
    @ApiModelProperty("偿付利息(元)")
    @JsonSerialize(using = AbsTenThousandToYuan2ScaleJsonSerializer.class, nullsUsing = AbsTenThousandToYuan2ScaleJsonSerializer.class)
    private BigDecimal payInterestCash;
    @ApiModelProperty("偿付本金(元)")
    @JsonSerialize(using = AbsTenThousandToYuan2ScaleJsonSerializer.class, nullsUsing = AbsTenThousandToYuan2ScaleJsonSerializer.class)
    private BigDecimal payPrincipalCash;
    @ApiModelProperty("行权偿付利息(元)")
    @JsonSerialize(using = AbsTenThousandToYuan2ScaleJsonSerializer.class, nullsUsing = AbsTenThousandToYuan2ScaleJsonSerializer.class)
    private BigDecimal exercisePayInterestCash;
    @ApiModelProperty("行权偿付本金(元)")
    @JsonSerialize(using = AbsTenThousandToYuan2ScaleJsonSerializer.class, nullsUsing = AbsTenThousandToYuan2ScaleJsonSerializer.class)
    private BigDecimal exercisePayPrincipalCash;
    @ApiModelProperty("理论付息日")
    @JsonSerialize(using = DisplayDateJsonWithFormatSerializer.class, nullsUsing = DisplayDateJsonWithFormatSerializer.class)
    private Date theoryInterestDate;


    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }

    public Date getDataDate() {
        return dataDate;
    }

    public void setDataDate(Date dataDate) {
        this.dataDate = dataDate;
    }

    public BigDecimal getPayInterestCash() {
        return payInterestCash;
    }

    public void setPayInterestCash(BigDecimal payInterestCash) {
        this.payInterestCash = payInterestCash;
    }

    public BigDecimal getPayPrincipalCash() {
        return payPrincipalCash;
    }

    public void setPayPrincipalCash(BigDecimal payPrincipalCash) {
        this.payPrincipalCash = payPrincipalCash;
    }

    public BigDecimal getExercisePayInterestCash() {
        return exercisePayInterestCash;
    }

    public void setExercisePayInterestCash(BigDecimal exercisePayInterestCash) {
        this.exercisePayInterestCash = exercisePayInterestCash;
    }

    public BigDecimal getExercisePayPrincipalCash() {
        return exercisePayPrincipalCash;
    }

    public void setExercisePayPrincipalCash(BigDecimal exercisePayPrincipalCash) {
        this.exercisePayPrincipalCash = exercisePayPrincipalCash;
    }

    public Date getTheoryInterestDate() {
        return theoryInterestDate;
    }

    public void setTheoryInterestDate(Date theoryInterestDate) {
        this.theoryInterestDate = theoryInterestDate;
    }

    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }

    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }
}

