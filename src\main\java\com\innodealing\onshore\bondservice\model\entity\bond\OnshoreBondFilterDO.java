package com.innodealing.onshore.bondservice.model.entity.bond;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.sql.Date;
import java.sql.Timestamp;

/**
 * 境内债券筛选信息表实体对象
 *
 * <AUTHOR>
 */
@Table(name = "onshore_bond_filter")
public class OnshoreBondFilterDO {
    /**
     * 主键id
     */
    @Id
    @Column
    private Long id;
    /**
     * 创建时间
     */
    @Column
    private Timestamp createTime;
    /**
     * 更新时间
     */
    @Column
    private Timestamp updateTime;
    /**
     * 是否删除：0： 未删除;1：已删除
     */
    @Column
    private Integer deleted;
    /**
     * 是否到期：0： 未到期;1: 已到期
     */
    @Column
    private Integer expired;
    /**
     * 发行状态：0: 发行中; 1: 已经上市; 2: 延迟发行; 3: 取消发行; 999:其他
     */
    @Column
    private Integer issueStatus;
    /**
     * 债券唯一编码
     */
    @Column
    private Long bondUniCode;
    /**
     * 主体唯一编码
     */
    @Column
    private Long comUniCode;
    /**
     * 主体简称
     */
    @Column
    private String comFullName;
    /**
     * 币种: 1: CNY; 2: HKD; 3: USD
     */
    @Column
    private Integer currency;
    /**
     * 是否城投主体: 0: 否；1：是
     */
    @Column
    private Integer udicStatus;
    /**
     * 一级行业编码
     */
    @Column
    private Long induLevel1Code;
    /**
     * 二级行业编码
     */
    @Column
    private Long induLevel2Code;
    /**
     * 详情参考参数字典： http://git.innodealing.cn/global/document/wikis/%E8%A7%84%E8%8C%83/%E5%B8%B8%E7%94%A8%E5%AD%97%E6%AE%B5%E8%A7%84%E8%8C%83
     */
    @Column
    private Integer businessNature;
    /**
     * 企业性质（经营类型过滤使用）1:央企, 2:国企, 3:民企, 999:其他
     */
    @Column
    private Integer businessFilterNature;
    /**
     * 担保: 0: 无; 1: 有;
     */
    @Column
    private Integer guaranteedStatus;
    /**
     * 募集: 1: 公募; 0: 私募
     */
    @Column
    private Integer publicOffering;
    /**
     * 利率类型: 0 其他; 1 固定利率; 2 浮动利率; 3 累进利率; 4:贴现; 5:无序利率
     */
    @Column
    private Integer interestRateType;
    /**
     * 1：固息; 2 浮息; 999：其他
     */
    @Column
    private Integer couponRateFilterType;
    /**
     * 含权：0:含权 1：不含权 2:永续
     */
    @Column
    private Integer embeddedOption;
    /**
     * 债券类型：参考数据字典bond_type: http://git.innodealing.cn/global/document/wikis/%E8%A7%84%E8%8C%83/%E5%B8%B8%E7%94%A8%E5%AD%97%E6%AE%B5%E8%A7%84%E8%8C%83
     */
    @Column
    private Integer bondType;
    /**
     * 债券筛选类型 1:国债, 2:央票, 3:国开, 4:非国开, 5:地方债, 6:短融, 7:中票, 8:企业债, 9:公司债, 10:金融债, 11:可转债, 12:可交换债, 13:ABS, 14:PPN, 15:存单, 999:其他
     */
    @Column
    private Integer bondFilterType;
    /**
     * 地级市编码
     */
    @Column
    private Long cityUniCode;
    /**
     * 省份编码
     */
    @Column
    private Long provinceUniCode;
    /**
     * 城投区域级别: 1： 直辖市级；2：省级； 3：计划单列市； 4：副省级城市；5：副省级国家级新区；6：直辖市区；7：强地级市；8：地级市；9：正厅级国家级新区；10：副厅级国家级新区；11：国家级开发区；12：百强区；13：省级开发区；14：百强县；15：一般区县
     */
    @Column
    private Integer udicAdministrativeRegion;
    /**
     * 城投(实际控制人)区域编码
     */
    @Column
    private Long udicAreaUniCode;
    /**
     * 城投(实际控制人)城市编码
     */
    @Column
    private Long udicCityUniCode;
    /**
     * 城投(实际控制人)省份编码
     */
    @Column
    private Long udicProvinceUniCode;
    /**
     * 主体外部评级
     */
    @Column
    private Integer comExtRatingMapping;
    /**
     * 主体外部评级筛选: 1 AAA; 2 AA+; 3: AA; 4: AA-; 5: A+; 999:其他
     */
    @Column
    private Integer comExtRatingFilterMapping;
    /**
     * 主体评级
     */
    @Column
    private String comExtRating;
    /**
     * 债券外部
     */
    @Column
    private String bondExtRating;
    /**
     * 主体yy评级
     */
    @Column
    private Integer comYyRatingMapping;
    /**
     * 海外主体外部评级
     */
    @Column
    private Integer intlExtRatingMapping;
    /**
     * 债券隐含评级
     */
    @Column
    private Integer bondImpliedRatingMapping;
    /**
     * 中债隐含评级
     */
    @Column
    private String bondImpliedRating;
    /**
     * 债券外部评级
     */
    @Column
    private Integer bondExtRatingMapping;
    /**
     * 债券外部评级筛选: 1 AAA; 2 AA+; 3: AA; 4: AA-; 5: A+; 999:其他
     */
    @Column
    private Integer bondExtRatingFilterMapping;
    /**
     * 是否回售： 0：否： 1：是
     */
    @Column
    private Integer putOptionStatus;
    /**
     * 是否赎回： 0：否; 1： 是
     */
    @Column
    private Integer redeemStatus;
    /**
     * 主体是否违约： 0: 没有违约; 1：已经违约
     */
    @Column
    private Integer defaultComStatus;
    /**
     * 1 深圳证券交易所;2 上海证券交易所; 3 银行间市场;4 柜台交易市场; 99 其他
     */
    @Column
    private Integer secondMarket;
    /**
     * 1 交易所; 2:银行间； 999: 其他
     */
    @Column
    private Integer secondFilterMarket;
    /**
     * 是否跨市场：0：否; 1: 是
     */
    @Column
    private Integer crossMarketStatus;
    /**
     * 是否流通中(0: 否； 1：是)
     */
    @Column
    private Integer circulationStatus;
    /**
     * 银行类型: 1: 政策性银行; 2: 国有商业银行;3: 股份制商业银行; 4:城市商业银行; 5: 农村商业银行; 6: 农村信用合作社; 7:村镇银行     这个和NCD相关
     */
    @Column
    private Integer bankType;
    /**
     * 上市状态   0:未上市   1:已经上市
     */
    @Column
    private Integer listedStatus;
    /**
     * 含权状态   0:不含权   1:含权
     */
    @Column
    private Integer embeddedOptionStatus;
    /**
     * 永续状态   0:非永续   1:永续
     */
    @Column
    private Integer perpetualStatus;
    /**
     * 质押状态   0:不可质押  1:可质押
     */
    @Column
    private Integer pledgeStatus;
    /**
     * 1：固息; 2：DEPO; 3:LPR; 4:SHIBOR; 999:其他;
     */
    @Column
    private Integer couponRateV3FilterType;
    /**
     * 剩余期限天数,已到期债券设置为-1
     */
    @Column
    private Integer remainingTenorDay;
    /**
     * 剩余期限
     */
    @Column
    private String remainingTenor;
    /**
     * 上市日期
     */
    @Column
    private Date listDate;
    /**
     * dm城投状态 1城投 0非城投
     */
    @Column
    private Integer dmUdicStatus;
    /**
     * 提前还本状态  0: 不提前还本   1: 提前还本
     */
    @Column
    private Integer prepaymentStatus;
    /**
     * 清偿顺序  1 普通债权,2 次级债权,3 二级资本工具,4 混合资本工具,5 其他一级资本工具,99 其他
     */
    @Column
    private Integer paymentOrder;
    /**
     * 剩余规模(万元)
     */
    @Column
    private BigDecimal bondBalance;
    /**
     * 最新票面利率(%)，优先取t_bond_basic_info表new_coup_rate，若该字段为空或为0，取ref_yield
     */
    @Column
    private BigDecimal latestCouponRate;
    /**
     * 跨市场去重状态： 1 跨市场债去重后展示或非跨市场债；0 跨市场债去重后不展示（去重规则按照sec_mar_par 3,2,6,1,78,4,其他顺序保留）
     */
    @Column
    private Integer crossMarketDedupStatus;
    /**
     * 城投评分
     */
    @Column
    private BigDecimal udicScore;
    /**
     * 地方债类型： 1 一般债; 2 专项债;  99 其他
     */
    @Column
    private Integer lgBondType;
    /**
     * 跨市场债券唯一编码
     */
    @Column
    private Long bondId;
    /**
     * 是否绿色债券 0:否  1:是
     */
    @Column
    private Integer greenBondStatus;
    /**
     * 主体YY评分映射第二版本
     */
    @Column
    private Integer comYyRatingV2Mapping;
    /**
     * 地方债资金用途性质: 1 新增; 2 再融资; 3 置换;  4 特殊再融资; 99 其他
     */
    @Column
    private Integer fundUseType;
    /**
     * 发行价
     */
    @Column
    private BigDecimal issuePrice;
    /**
     * 发行开始日期
     */
    @Column
    private Date issueStartDate;
    /**
     * 债券代码（外部）
     */
    @Column
    private String bondCode;
    /**
     * 债券简称
     */
    @Column
    private String bondShortName;
    /**
     * 是否可调整票面 1：是；0：否；
     */
    @Column
    private Integer couponAdjustableStatus;
    /**
     * 债券当前(存续)状态 1：是；0：否；
     */
    @Column
    private Integer outstandingStatus;
    /**
     * 到期日期
     */
    @Column
    private Date maturityDate;
    /**
     * 下一行权日 t_bond_basic_info.exer_pay_date取第一个逗号前的日期
     */
    @Column
    private String exerciseDate;
    /**
     * 中债到期收益率
     */
    @Column
    private BigDecimal cbYtm;
    /**
     * 中债行权收益率
     */
    @Column
    private BigDecimal cbYte;
    /**
     * 中证到期收益率
     */
    @Column
    private BigDecimal csYtm;
    /**
     * 中证行权收益率
     */
    @Column
    private BigDecimal csYte;
    /**
     * 注册通知书
     */
    @Column
    private String regNoticeNumber;
    /**
     * 募集资金用途
     */
    @Column
    private String capitalCollectionUsage;
    /**
     * 主承销商
     */
    @Column
    private String leadUnderwriter;
    /**
     * 担保人
     */
    @Column
    private String guarantor;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }


    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }


    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }


    public Integer getDeleted() {
        return deleted;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }


    public Integer getExpired() {
        return expired;
    }

    public void setExpired(Integer expired) {
        this.expired = expired;
    }


    public Integer getIssueStatus() {
        return issueStatus;
    }

    public void setIssueStatus(Integer issueStatus) {
        this.issueStatus = issueStatus;
    }


    public Long getBondUniCode() {
        return bondUniCode;
    }

    public void setBondUniCode(Long bondUniCode) {
        this.bondUniCode = bondUniCode;
    }


    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }


    public String getComFullName() {
        return comFullName;
    }

    public void setComFullName(String comFullName) {
        this.comFullName = comFullName;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public Integer getUdicStatus() {
        return udicStatus;
    }

    public void setUdicStatus(Integer udicStatus) {
        this.udicStatus = udicStatus;
    }

    public Long getInduLevel1Code() {
        return induLevel1Code;
    }

    public void setInduLevel1Code(Long induLevel1Code) {
        this.induLevel1Code = induLevel1Code;
    }

    public Long getInduLevel2Code() {
        return induLevel2Code;
    }

    public void setInduLevel2Code(Long induLevel2Code) {
        this.induLevel2Code = induLevel2Code;
    }


    public Integer getBusinessNature() {
        return businessNature;
    }

    public void setBusinessNature(Integer businessNature) {
        this.businessNature = businessNature;
    }


    public Integer getBusinessFilterNature() {
        return businessFilterNature;
    }

    public void setBusinessFilterNature(Integer businessFilterNature) {
        this.businessFilterNature = businessFilterNature;
    }


    public Integer getPublicOffering() {
        return publicOffering;
    }

    public void setPublicOffering(Integer publicOffering) {
        this.publicOffering = publicOffering;
    }


    public Integer getInterestRateType() {
        return interestRateType;
    }

    public void setInterestRateType(Integer interestRateType) {
        this.interestRateType = interestRateType;
    }

    public Integer getRedeemStatus() {
        return redeemStatus;
    }

    public void setRedeemStatus(Integer redeemStatus) {
        this.redeemStatus = redeemStatus;
    }

    public Integer getCouponAdjustableStatus() {
        return couponAdjustableStatus;
    }

    public void setCouponAdjustableStatus(Integer couponAdjustableStatus) {
        this.couponAdjustableStatus = couponAdjustableStatus;
    }

    public Integer getCouponRateFilterType() {
        return couponRateFilterType;
    }

    public void setCouponRateFilterType(Integer couponRateFilterType) {
        this.couponRateFilterType = couponRateFilterType;
    }


    public Integer getEmbeddedOption() {
        return embeddedOption;
    }

    public void setEmbeddedOption(Integer embeddedOption) {
        this.embeddedOption = embeddedOption;
    }


    public Integer getBondType() {
        return bondType;
    }

    public void setBondType(Integer bondType) {
        this.bondType = bondType;
    }


    public Integer getBondFilterType() {
        return bondFilterType;
    }

    public void setBondFilterType(Integer bondFilterType) {
        this.bondFilterType = bondFilterType;
    }


    public Long getCityUniCode() {
        return cityUniCode;
    }

    public void setCityUniCode(Long cityUniCode) {
        this.cityUniCode = cityUniCode;
    }


    public Long getProvinceUniCode() {
        return provinceUniCode;
    }

    public void setProvinceUniCode(Long provinceUniCode) {
        this.provinceUniCode = provinceUniCode;
    }


    public Integer getUdicAdministrativeRegion() {
        return udicAdministrativeRegion;
    }

    public void setUdicAdministrativeRegion(Integer udicAdministrativeRegion) {
        this.udicAdministrativeRegion = udicAdministrativeRegion;
    }


    public Long getUdicAreaUniCode() {
        return udicAreaUniCode;
    }

    public void setUdicAreaUniCode(Long udicAreaUniCode) {
        this.udicAreaUniCode = udicAreaUniCode;
    }


    public Long getUdicCityUniCode() {
        return udicCityUniCode;
    }

    public void setUdicCityUniCode(Long udicCityUniCode) {
        this.udicCityUniCode = udicCityUniCode;
    }


    public Long getUdicProvinceUniCode() {
        return udicProvinceUniCode;
    }

    public void setUdicProvinceUniCode(Long udicProvinceUniCode) {
        this.udicProvinceUniCode = udicProvinceUniCode;
    }


    public Integer getComExtRatingMapping() {
        return comExtRatingMapping;
    }

    public void setComExtRatingMapping(Integer comExtRatingMapping) {
        this.comExtRatingMapping = comExtRatingMapping;
    }


    public Integer getComExtRatingFilterMapping() {
        return comExtRatingFilterMapping;
    }

    public void setComExtRatingFilterMapping(Integer comExtRatingFilterMapping) {
        this.comExtRatingFilterMapping = comExtRatingFilterMapping;
    }


    public String getComExtRating() {
        return comExtRating;
    }

    public void setComExtRating(String comExtRating) {
        this.comExtRating = comExtRating;
    }


    public String getBondExtRating() {
        return bondExtRating;
    }

    public void setBondExtRating(String bondExtRating) {
        this.bondExtRating = bondExtRating;
    }


    public Integer getComYyRatingMapping() {
        return comYyRatingMapping;
    }

    public void setComYyRatingMapping(Integer comYyRatingMapping) {
        this.comYyRatingMapping = comYyRatingMapping;
    }


    public Integer getIntlExtRatingMapping() {
        return intlExtRatingMapping;
    }

    public void setIntlExtRatingMapping(Integer intlExtRatingMapping) {
        this.intlExtRatingMapping = intlExtRatingMapping;
    }


    public Integer getBondImpliedRatingMapping() {
        return bondImpliedRatingMapping;
    }

    public void setBondImpliedRatingMapping(Integer bondImpliedRatingMapping) {
        this.bondImpliedRatingMapping = bondImpliedRatingMapping;
    }


    public String getBondImpliedRating() {
        return bondImpliedRating;
    }

    public void setBondImpliedRating(String bondImpliedRating) {
        this.bondImpliedRating = bondImpliedRating;
    }


    public Integer getBondExtRatingMapping() {
        return bondExtRatingMapping;
    }

    public void setBondExtRatingMapping(Integer bondExtRatingMapping) {
        this.bondExtRatingMapping = bondExtRatingMapping;
    }


    public Integer getBondExtRatingFilterMapping() {
        return bondExtRatingFilterMapping;
    }

    public void setBondExtRatingFilterMapping(Integer bondExtRatingFilterMapping) {
        this.bondExtRatingFilterMapping = bondExtRatingFilterMapping;
    }


    public Integer getPutOptionStatus() {
        return putOptionStatus;
    }

    public void setPutOptionStatus(Integer putOptionStatus) {
        this.putOptionStatus = putOptionStatus;
    }

    public Integer getDefaultComStatus() {
        return defaultComStatus;
    }

    public void setDefaultComStatus(Integer defaultComStatus) {
        this.defaultComStatus = defaultComStatus;
    }


    public Integer getSecondMarket() {
        return secondMarket;
    }

    public void setSecondMarket(Integer secondMarket) {
        this.secondMarket = secondMarket;
    }


    public Integer getSecondFilterMarket() {
        return secondFilterMarket;
    }

    public void setSecondFilterMarket(Integer secondFilterMarket) {
        this.secondFilterMarket = secondFilterMarket;
    }


    public Integer getCrossMarketStatus() {
        return crossMarketStatus;
    }

    public void setCrossMarketStatus(Integer crossMarketStatus) {
        this.crossMarketStatus = crossMarketStatus;
    }


    public Integer getCirculationStatus() {
        return circulationStatus;
    }

    public void setCirculationStatus(Integer circulationStatus) {
        this.circulationStatus = circulationStatus;
    }


    public Integer getBankType() {
        return bankType;
    }

    public void setBankType(Integer bankType) {
        this.bankType = bankType;
    }


    public Integer getListedStatus() {
        return listedStatus;
    }

    public void setListedStatus(Integer listedStatus) {
        this.listedStatus = listedStatus;
    }


    public Integer getEmbeddedOptionStatus() {
        return embeddedOptionStatus;
    }

    public void setEmbeddedOptionStatus(Integer embeddedOptionStatus) {
        this.embeddedOptionStatus = embeddedOptionStatus;
    }


    public Integer getPerpetualStatus() {
        return perpetualStatus;
    }

    public void setPerpetualStatus(Integer perpetualStatus) {
        this.perpetualStatus = perpetualStatus;
    }


    public Integer getPledgeStatus() {
        return pledgeStatus;
    }

    public void setPledgeStatus(Integer pledgeStatus) {
        this.pledgeStatus = pledgeStatus;
    }


    public Integer getCouponRateV3FilterType() {
        return couponRateV3FilterType;
    }

    public void setCouponRateV3FilterType(Integer couponRateV3FilterType) {
        this.couponRateV3FilterType = couponRateV3FilterType;
    }


    public Integer getRemainingTenorDay() {
        return remainingTenorDay;
    }

    public void setRemainingTenorDay(Integer remainingTenorDay) {
        this.remainingTenorDay = remainingTenorDay;
    }


    public String getRemainingTenor() {
        return remainingTenor;
    }

    public void setRemainingTenor(String remainingTenor) {
        this.remainingTenor = remainingTenor;
    }


    public Date getListDate() {
        return listDate;
    }

    public void setListDate(Date listDate) {
        this.listDate = listDate;
    }


    public Integer getDmUdicStatus() {
        return dmUdicStatus;
    }

    public void setDmUdicStatus(Integer dmUdicStatus) {
        this.dmUdicStatus = dmUdicStatus;
    }


    public Integer getGuaranteedStatus() {
        return guaranteedStatus;
    }

    public void setGuaranteedStatus(Integer guaranteedStatus) {
        this.guaranteedStatus = guaranteedStatus;
    }


    public Integer getPrepaymentStatus() {
        return prepaymentStatus;
    }

    public void setPrepaymentStatus(Integer prepaymentStatus) {
        this.prepaymentStatus = prepaymentStatus;
    }


    public Integer getPaymentOrder() {
        return paymentOrder;
    }

    public void setPaymentOrder(Integer paymentOrder) {
        this.paymentOrder = paymentOrder;
    }


    public BigDecimal getBondBalance() {
        return bondBalance;
    }

    public void setBondBalance(BigDecimal bondBalance) {
        this.bondBalance = bondBalance;
    }


    public BigDecimal getLatestCouponRate() {
        return latestCouponRate;
    }

    public void setLatestCouponRate(BigDecimal latestCouponRate) {
        this.latestCouponRate = latestCouponRate;
    }


    public Integer getCrossMarketDedupStatus() {
        return crossMarketDedupStatus;
    }

    public void setCrossMarketDedupStatus(Integer crossMarketDedupStatus) {
        this.crossMarketDedupStatus = crossMarketDedupStatus;
    }


    public BigDecimal getUdicScore() {
        return udicScore;
    }

    public void setUdicScore(BigDecimal udicScore) {
        this.udicScore = udicScore;
    }


    public Integer getLgBondType() {
        return lgBondType;
    }

    public void setLgBondType(Integer lgBondType) {
        this.lgBondType = lgBondType;
    }


    public Long getBondId() {
        return bondId;
    }

    public void setBondId(Long bondId) {
        this.bondId = bondId;
    }


    public Integer getGreenBondStatus() {
        return greenBondStatus;
    }

    public void setGreenBondStatus(Integer greenBondStatus) {
        this.greenBondStatus = greenBondStatus;
    }


    public Integer getComYyRatingV2Mapping() {
        return comYyRatingV2Mapping;
    }

    public void setComYyRatingV2Mapping(Integer comYyRatingV2Mapping) {
        this.comYyRatingV2Mapping = comYyRatingV2Mapping;
    }


    public Integer getFundUseType() {
        return fundUseType;
    }

    public void setFundUseType(Integer fundUseType) {
        this.fundUseType = fundUseType;
    }


    public BigDecimal getIssuePrice() {
        return issuePrice;
    }

    public void setIssuePrice(BigDecimal issuePrice) {
        this.issuePrice = issuePrice;
    }


    public Date getIssueStartDate() {
        return issueStartDate;
    }

    public void setIssueStartDate(Date issueStartDate) {
        this.issueStartDate = issueStartDate;
    }


    public String getBondCode() {
        return bondCode;
    }

    public void setBondCode(String bondCode) {
        this.bondCode = bondCode;
    }


    public String getBondShortName() {
        return bondShortName;
    }

    public void setBondShortName(String bondShortName) {
        this.bondShortName = bondShortName;
    }

    public Integer getOutstandingStatus() {
        return outstandingStatus;
    }

    public void setOutstandingStatus(Integer outstandingStatus) {
        this.outstandingStatus = outstandingStatus;
    }

    public Date getMaturityDate() {
        return maturityDate;
    }

    public void setMaturityDate(Date maturityDate) {
        this.maturityDate = maturityDate;
    }


    public String getExerciseDate() {
        return exerciseDate;
    }

    public void setExerciseDate(String exerciseDate) {
        this.exerciseDate = exerciseDate;
    }


    public BigDecimal getCbYtm() {
        return cbYtm;
    }

    public void setCbYtm(BigDecimal cbYtm) {
        this.cbYtm = cbYtm;
    }


    public BigDecimal getCbYte() {
        return cbYte;
    }

    public void setCbYte(BigDecimal cbYte) {
        this.cbYte = cbYte;
    }


    public BigDecimal getCsYtm() {
        return csYtm;
    }

    public void setCsYtm(BigDecimal csYtm) {
        this.csYtm = csYtm;
    }


    public BigDecimal getCsYte() {
        return csYte;
    }

    public void setCsYte(BigDecimal csYte) {
        this.csYte = csYte;
    }


    public String getRegNoticeNumber() {
        return regNoticeNumber;
    }

    public void setRegNoticeNumber(String regNoticeNumber) {
        this.regNoticeNumber = regNoticeNumber;
    }


    public String getCapitalCollectionUsage() {
        return capitalCollectionUsage;
    }

    public void setCapitalCollectionUsage(String capitalCollectionUsage) {
        this.capitalCollectionUsage = capitalCollectionUsage;
    }


    public String getLeadUnderwriter() {
        return leadUnderwriter;
    }

    public void setLeadUnderwriter(String leadUnderwriter) {
        this.leadUnderwriter = leadUnderwriter;
    }


    public String getGuarantor() {
        return guarantor;
    }

    public void setGuarantor(String guarantor) {
        this.guarantor = guarantor;
    }

}

