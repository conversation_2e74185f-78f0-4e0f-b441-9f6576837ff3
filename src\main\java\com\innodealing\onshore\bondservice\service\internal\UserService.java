package com.innodealing.onshore.bondservice.service.internal;


import com.innodealing.onshore.bondmetadata.dto.cbpermission.CBBondPermissionResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.cbpermission.CBPermissionDateResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.cbpermission.CBPermissionResponseDTO;
import com.innodealing.onshore.bondmetadata.dto.user.UserBrokersAuthDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.sql.Date;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 授权服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "onshore-user-service", url = "${user.service.api.url}", path = "/internal")
public interface UserService {

    /**
     * 获取用户经纪商权限, 该方法可能会返回null，所以进行了二次封装
     *
     * @param userId 用户id
     * @return 用户经纪商权限
     */
    @GetMapping("/user/broker/auth")
    @Nullable
    UserBrokersAuthDTO getUserBrokersAuthNullable(@RequestParam Long userId);

    /**
     * 判断用户是否有CFETS权限
     *
     * @param userId 用户id
     * @return true：有  false：没有
     */
    @GetMapping("/permission/has-cfets/resources")
    boolean hasCfetsResourcePermission(@RequestParam Long userId);

    /**
     * 获取中债当年和历史分割点
     *
     * @return 当年和历史时间点
     */
    @GetMapping("/cb/permission/current-year")
    Date getCBCurrentYear();

    /**
     * 获取这个用户某个债券所拥有的权限资源
     *
     * @param userid       用户Id
     * @param bondUniCodes 债券编码 最大限制为1000个
     * @return 用户债券所拥有的权限资源
     */
    @PostMapping("/cb/permission/date")
    List<CBBondPermissionResponseDTO> listCbBondPermissionByDate(@RequestParam Long userid, @RequestBody Long[] bondUniCodes);

    /**
     * userid 获取用户所在机构中债权限
     *
     * @param userid 用户Id
     * @return 中债权限列表
     */
    @GetMapping("/cb/permission")
    List<CBPermissionResponseDTO> listCBPermission(@RequestParam("userid") Long userid);

    /**
     * 获取有中债权限的日期(根据日期)
     *
     * @param userId      用户id
     * @param bondUniCode 债券唯一编码
     * @return 中债权限集合
     */
    default List<CBPermissionDateResponseDTO> listCBPermissionDateResponses(Long userId, Long bondUniCode) {
        List<CBBondPermissionResponseDTO> cbBondPermissionResponseDTOList =
                this.listCbBondPermissionByDate(userId, new Long[]{bondUniCode});
        if (CollectionUtils.isEmpty(cbBondPermissionResponseDTOList)) {
            return Collections.emptyList();
        }
        return cbBondPermissionResponseDTOList.get(0)
                .getCbPermissionResponseDTOs();
    }

    /**
     * 获取中债权限(根据日期)
     *
     * @param cbPermissionDateResponseDTOList 中债权限集合
     * @param issueDate                       日期
     * @return true 有权限 false无权限
     */
    default boolean hasCBPermissionByDate(List<CBPermissionDateResponseDTO> cbPermissionDateResponseDTOList, Date issueDate) {
        if (issueDate == null) {
            return false;
        }
        if (CollectionUtils.isNotEmpty(cbPermissionDateResponseDTOList)) {
            for (CBPermissionDateResponseDTO cbPermissionDateResponseDTO : cbPermissionDateResponseDTOList) {
                if (issueDate.compareTo(cbPermissionDateResponseDTO.getStartDate()) >= 0 &&
                        issueDate.compareTo(cbPermissionDateResponseDTO.getEndDate()) <= 0) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 获取用户经纪商权限
     *
     * @param userId 用户id
     * @return 用户经纪商权限
     */
    @Nonnull
    default UserBrokersAuthDTO getUserBrokersAuth(Long userId) {
        UserBrokersAuthDTO userBrokersAuthDTO = this.getUserBrokersAuthNullable(userId);
        if (Objects.isNull(userBrokersAuthDTO)) {
            return new UserBrokersAuthDTO();
        }
        return userBrokersAuthDTO;
    }

    /**
     * 是否有中债权限
     *
     * @param userId 用户id
     * @return 中债权限
     */
    default boolean hasCbPermission(Long userId) {
        return this.getUserBrokersAuth(userId).isCbAuthFlag();
    }

    /**
     * 是否有中债权限
     *
     * @param userId 用户id
     * @return 中债权限
     */
    default boolean hasCsPermission(Long userId) {
        return this.getUserBrokersAuth(userId).isCsAuthFlag();
    }

    /**
     * 是否有中债权限
     *
     * @param userId 用户id
     * @return 中债权限
     */
    default boolean hasCbBondImpliedRatingPermission(Long userId) {
        Map<String, Boolean> cbPermissionMap = this.getCBPermissionMap(userId);
        Boolean bondImpliedRating = cbPermissionMap.get("Bond_ImpliedRating");
        if (Objects.isNull(bondImpliedRating)) {
            return false;
        }
        return bondImpliedRating;
    }

    /**
     * 中债权限map
     *
     * @param userId 用户id
     * @return key 权限code,value 是否有权限
     */
    default Map<String, Boolean> getCBPermissionMap(Long userId) {
        List<CBPermissionResponseDTO> cbPermissionResponseDTOList = this.listCBPermission(userId);
        return cbPermissionResponseDTOList
                .stream()
                .collect(Collectors.toMap(CBPermissionResponseDTO::getAuthCode, CBPermissionResponseDTO::getHasPermission, (x1, x2) -> x2));
    }

    /**
     * 获取用户页面列表header展示的字段配置
     *
     * @param userId   userId
     * @param tableKey key
     * @return 字段配置
     */
    @GetMapping("/user/table/header/config/table-header-json")
    String getUserTableHeaderJson(@RequestParam Long userId,
                                  @RequestParam String tableKey);
}
