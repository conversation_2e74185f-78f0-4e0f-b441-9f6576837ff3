package com.innodealing.onshore.bondservice.model.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

import java.util.Objects;

/**
 * 时间类型枚举
 *
 * <AUTHOR>
 * @date 2025/01/23
 */
public enum DateTypeEnum implements ITextValueEnum {

    /**
     * 年度数据
     */
    YEAR(1, "年度"),

    /**
     * 季度数据
     */
    QUARTER(2, "季度"),

    /**
     * 月度数据
     */
    MONTH(3, "月度");

    private final Integer value;
    private final String text;

    DateTypeEnum(Integer value, String text) {
        this.value = value;
        this.text = text;
    }


    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

    /**
     * 根据代码值获取对应的时间类型
     *
     * @param code 代码值
     * @return 对应的时间类型枚举
     * @throws IllegalArgumentException 当传入不支持的代码值时
     */
    public static DateTypeEnum fromCode(Integer code) {
        if (code == null) {
            throw new IllegalArgumentException("时间类型代码不能为空");
        }
        for (DateTypeEnum dateTypeEnum : DateTypeEnum.values()) {
            if (Objects.equals(dateTypeEnum.getValue(), code)) {
                return dateTypeEnum;
            }
        }
        throw new IllegalArgumentException("不支持的时间类型代码: " + code);
    }
}