package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.chinabond.DwdCbValuationDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.sql.Date;
import java.util.List;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "onshore-dwd-china-bond", url = "${onshore.dwd.china.bond.url}", path = "/internal")
public interface OnshoreDwdChinaBondHttpService {

    /**
     * 根据日期和bondUniCode集合获取中证估值数据
     *
     * @param valuationDate 日期
     * @param bondUniCodes  bondUniCodes集合
     * @return 数据集合
     */
    @PostMapping("/dwd/cb/valuation/bond-uni-code/list")
    List<DwdCbValuationDTO> listDwdCbValuationDTOs(@RequestBody List<Long> bondUniCodes, @RequestParam("valuationDate") Date valuationDate);


}
