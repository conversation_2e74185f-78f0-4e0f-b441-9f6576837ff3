package com.innodealing.onshore.bondservice.model.dto.response;

import com.innodealing.onshore.bondmetadata.dto.bondbasic.ComBondDetailDTO;
import com.innodealing.onshore.bondservice.model.dto.AbsBondShortInfoDTO;
import com.innodealing.onshore.bondservice.model.dto.RelationComShortInfoDTO;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 流通中债券-规模分布明细分页响应参数
 *
 * <AUTHOR>
 */
public class ComDistributionDetailResponseDTO {

    @ApiModelProperty("主体唯一代码")
    private Long comUniCode;
    @ApiModelProperty("是否为民企：0:否，1：是")
    private Integer privateEnterpriseStatus;
    @ApiModelProperty("相同实控人信息")
    private List<RelationComShortInfoDTO> relationEnterprises;
    @ApiModelProperty("abs信息")
    private List<AbsBondShortInfoDTO> absBondShortInfos;
    @ApiModelProperty("债券信息")
    private List<ComBondDetailDTO>  comBondDetailDTOS;
    @ApiModelProperty("相同实控人信息债券信息")
    private List<ComBondDetailDTO> relationComBondDetailDTOS;
    @ApiModelProperty("abs信息债券信息")
    private List<ComBondDetailDTO> absBondDetailDTOS;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getPrivateEnterpriseStatus() {
        return privateEnterpriseStatus;
    }

    public void setPrivateEnterpriseStatus(Integer privateEnterpriseStatus) {
        this.privateEnterpriseStatus = privateEnterpriseStatus;
    }

    public List<RelationComShortInfoDTO> getRelationEnterprises() {
        return relationEnterprises;
    }

    public void setRelationEnterprises(List<RelationComShortInfoDTO> relationEnterprises) {
        this.relationEnterprises = relationEnterprises;
    }

    public List<AbsBondShortInfoDTO> getAbsBondShortInfos() {
        return absBondShortInfos;
    }

    public void setAbsBondShortInfos(List<AbsBondShortInfoDTO> absBondShortInfos) {
        this.absBondShortInfos = absBondShortInfos;
    }

    public List<ComBondDetailDTO> getComBondDetailDTOS() {
        return comBondDetailDTOS;
    }

    public void setComBondDetailDTOS(List<ComBondDetailDTO> comBondDetailDTOS) {
        this.comBondDetailDTOS = comBondDetailDTOS;
    }

    public List<ComBondDetailDTO> getRelationComBondDetailDTOS() {
        return relationComBondDetailDTOS;
    }

    public void setRelationComBondDetailDTOS(List<ComBondDetailDTO> relationComBondDetailDTOS) {
        this.relationComBondDetailDTOS = relationComBondDetailDTOS;
    }

    public List<ComBondDetailDTO> getAbsBondDetailDTOS() {
        return absBondDetailDTOS;
    }

    public void setAbsBondDetailDTOS(List<ComBondDetailDTO> absBondDetailDTOS) {
        this.absBondDetailDTOS = absBondDetailDTOS;
    }
}

