package com.innodealing.onshore.bondservice.model.bo;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 筛选项bo
 *
 * <AUTHOR>
 * @create: 2024-12-02
 */
public class OptionalBO {
    @JsonProperty("value")
    private String value;
    @JsonProperty("disabled")
    private Boolean disabled;
    @JsonProperty("checked")
    private Boolean checked;
    @JsonProperty("name")
    private String name;
    @JsonProperty("canDrag")
    private Boolean canDrag;
    @JsonProperty("canDrop")
    private Boolean canDrop;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Boolean isDisabled() {
        return disabled;
    }

    public void setDisabled(<PERSON>ole<PERSON> disabled) {
        this.disabled = disabled;
    }

    public Boolean isChecked() {
        return checked;
    }

    public void setChecked(Boolean checked) {
        this.checked = checked;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean isCanDrag() {
        return canDrag;
    }

    public void setCanDrag(Boolean canDrag) {
        this.canDrag = canDrag;
    }

    public Boolean isCanDrop() {
        return canDrop;
    }

    public void setCanDrop(Boolean canDrop) {
        this.canDrop = canDrop;
    }
}
