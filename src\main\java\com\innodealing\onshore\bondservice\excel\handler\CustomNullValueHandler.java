package com.innodealing.onshore.bondservice.excel.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;

import java.util.List;
import java.util.Objects;

/**
 * 自定义空值处理器
 *
 * <AUTHOR>
 * @date 2023/10/17
 */
public class CustomNullValueHandler implements CellWriteHandler {

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                 List<WriteCellData<?>> <PERSON><PERSON><PERSON><PERSON><PERSON>, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (!isHead && (Objects.equals(cell.getCellType(), CellType.STRING) || Objects.equals(cell.getCellType(), CellType.BLANK))) {
            String cellValue = cell.getStringCellValue();
            if (StringUtils.isBlank(cellValue)) {
                cell.setCellValue("--");
            }
        }
    }
}