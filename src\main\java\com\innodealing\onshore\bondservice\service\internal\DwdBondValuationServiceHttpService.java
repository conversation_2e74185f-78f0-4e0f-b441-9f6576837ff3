package com.innodealing.onshore.bondservice.service.internal;

import com.innodealing.onshore.bondmetadata.dto.bond.valuation.CsBondValuationBusinessDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;
import java.sql.Date;

/**
 * 债券基础信息远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(name = "dwd-bond-valuation-service", url = "${dwd.bond.valuation.service.url}", path = "/internal")
public interface DwdBondValuationServiceHttpService {

    /**
     * 根据日期和bondUniCode集合获取中证估值数据
     *
     * @param date 日期
     * @param bondUniCodes  bondUniCodes集合
     * @return 数据集合
     */
    @PostMapping("/cs-dwd/valuation/list/date-uni-codes/cross-market")
    List<CsBondValuationBusinessDTO> listBondCsValuationByDateAndUniCodes(@ApiParam(value = "date", name = "date")
                                                                          @RequestParam(value = "date") Date date,
                                                                          @RequestBody Collection<Long> bondUniCodes);


}
