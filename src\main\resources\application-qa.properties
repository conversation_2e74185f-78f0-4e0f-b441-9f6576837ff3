server.port=8082
server.servlet.context-path=/onshore-bond-service
POLARDB_PROFILES_ACTIVE=qa_
# Druid
spring.datasource.druid.stat-view-servlet.enabled=true
spring.datasource.druid.stat-view-servlet.allow=
spring.datasource.druid.filter.stat.enabled=true
spring.datasource.druid.web-stat-filter.enabled=true
# mysql
#udic
bond.datasource.url=************************************************************/${POLARDB_PROFILES_ACTIVE:}bond?useUnicode=true&characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull&serverTimezone=GMT%2b8&rewriteBatchedStatements=true&useSSL=false&allowMultiQueries=true
bond.datasource.username=ops_qa_polar
bond.datasource.password=804QjW9Qk6V7EAEPZeMa
bond.datasource.driver-class-name=com.mysql.jdbc.Driver
bond.datasource.db-type=mysql
bond.datasource.use-global-data-source-stat=true
bond.datasource.pool-prepared-statements=true
bond.datasource.filters=stat,wall
bond.datasource.test-while-idle=true
bond.datasource.initial-size=5
bond.datasource.min-idle=5
bond.datasource.max-active=50
bond.datasource.time-between-eviction-runs-millis=60000
bond.datasource.druid.min-evictable-idle-time-millis=300000
bond.datasource.validation-query=SELECT 1

#pg
pgbond.datasource.url=***************************************************************************************************************************
pgbond.datasource.username=adbadmin
pgbond.datasource.password=qicY30WxyJ123
pgbond.datasource.driver-class-name=org.postgresql.Driver
pgbond.datasource.db-type=postgresql
pgbond.datasource.use-global-data-source-stat=true
pgbond.datasource.pool-prepared-statements=true
pgbond.datasource.filters=stat
pgbond.datasource.initial-size=5
pgbond.datasource.min-idle=5
pgbond.datasource.max-active=50
pgbond.datasource.time-between-eviction-runs-millis=60000
pgbond.datasource.validation-query=SELECT 'x'

# httpinnodealing
feign.httpclient.enabled=true
feign.client.config.default.connect-timeout=120000
feign.client.config.default.read-timeout=120000
logging.level.com.innodealing.onshore.udicservice.mapper=debug

# rocketmq
rocketmq.name-server=************:9876
rocketmq.producer.access-key=superadmin
rocketmq.producer.secret-key=12345678
rocketmq.producer.group=onshore-area-service-producer
# codis
spring.redis.host=r-uf6whjg0sb78oqq66b.redis.rds.aliyuncs.com
spring.redis.port=6379
spring.redis.password=t8Vq*WbtQXc#5
spring.redis.database=0
#redis
redisson.redis.host=r-uf6whjg0sb78oqq66b.redis.rds.aliyuncs.com
redisson.redis.port=6379
redisson.redis.database=0
redisson.redis.password=t8Vq*WbtQXc#5

ONSHORE_BOND_BASIC_URL=http://restuat.innodealing.com/onshore-bond-basic
bond.basic.api.url=http://restuat.innodealing.com/onshore-bond-basic
com.service.api.url=http://restuat.innodealing.com/onshore-com-service
bond.price.api.url=http://rest.innodealing.com/onshore-bond-price
area.service.api.url=http://restuat.innodealing.com/onshore-area-service
bond.sentiment.api.url=http://restuat.innodealing.com/onshore-sentiment-service
bond.finance.api.url=http://restuat.innodealing.com/onshore-bond-finance
onshore.udic.run.env=dev
user.service.api.url=http://restuat.innodealing.com/onshore-user-service
bond.rating.api.url=http://restuat.innodealing.com/onshore-bond-rating
yield.spread.api.url=http://restuat.innodealing.com/onshore-yield-spread
bond.radar.api.url=http://restuat.innodealing.com/onshore-bond-radar
bond.abs.api.url=http://restuat.innodealing.com/onshore-bond-abs
dws.bond.info.url=http://restuat.innodealing.com/dws-bond-info-service
dws.party.info.url=http://restuat.innodealing.com/dws-party-info-service
onshore.uid.service.url=http://restuat.innodealing.com/onshore-uid-service
dwd.bond.valuation.service.url=http://restuat.innodealing.com/dwd-bond-valuation-service
onshore.dwd.china.bond.url=http://restuat.innodealing.com/onshore-dwd-china-bond
onshore.com.url=http://restuat.innodealing.com/onshore-com-service
# http url
onshore.management.url=https://restuat.innodealing.com/onshore-management
industry.classification.management.url=http://restuat.innodealing.com/industry-classification-management
logging.level.com.innodealing.onshore.bondservice.mapper=debug
