package com.innodealing.onshore.bondservice.model.dto.request;


import com.github.wz2cool.dynamic.SortDirection;
import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

/**
 * 流通中债券-规模分布明细请求参数
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public class BondCashDistributionDetailPageRequestDTO {

    @ApiModelProperty("主体唯一编码")
    private Long comUniCode;
    @ApiModelProperty("相同实控人状态 0.否 1.是 ")
    private Integer ctrlTypeStatus;
    @ApiModelProperty("作为资产支持证券的共同债务人状态 0.否 1.是")
    private Integer absTypeStatus;

    @ApiModelProperty("债券code")
    private List<Long> bondUniCodes;
    @ApiModelProperty("开始日期")
    private Date startDate;
    @ApiModelProperty("结束日期")
    private Date endDate;
    @ApiModelProperty("开始页数")
    private Integer pageNum;
    @ApiModelProperty("每页大小")
    private Integer pageSize;
    @ApiModelProperty("排序属性")
    private String sortProperty;
    @ApiModelProperty("排序方向：ASC: 升序; DESC: 降序")
    private SortDirection sortDirection;
    @ApiModelProperty(value = "主体唯一编码集合", hidden = true)
    private List<Long> comUniCodes;

    public Long getComUniCode() {
        return comUniCode;
    }

    public void setComUniCode(Long comUniCode) {
        this.comUniCode = comUniCode;
    }

    public Integer getCtrlTypeStatus() {
        return ctrlTypeStatus;
    }

    public void setCtrlTypeStatus(Integer ctrlTypeStatus) {
        this.ctrlTypeStatus = ctrlTypeStatus;
    }

    public Integer getAbsTypeStatus() {
        return absTypeStatus;
    }

    public void setAbsTypeStatus(Integer absTypeStatus) {
        this.absTypeStatus = absTypeStatus;
    }

    public List<Long> getBondUniCodes() {
        return bondUniCodes;
    }

    public void setBondUniCodes(List<Long> bondUniCodes) {
        this.bondUniCodes = bondUniCodes;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getSortProperty() {
        return sortProperty;
    }

    public void setSortProperty(String sortProperty) {
        this.sortProperty = sortProperty;
    }

    public SortDirection getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(SortDirection sortDirection) {
        this.sortDirection = sortDirection;
    }

    public List<Long> getComUniCodes() {
        return comUniCodes;
    }

    public void setComUniCodes(List<Long> comUniCodes) {
        this.comUniCodes = comUniCodes;
    }
}
