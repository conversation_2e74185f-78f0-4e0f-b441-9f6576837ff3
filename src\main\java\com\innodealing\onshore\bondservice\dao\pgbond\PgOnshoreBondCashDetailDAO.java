package com.innodealing.onshore.bondservice.dao.pgbond;

import com.github.wz2cool.dynamic.*;
import com.github.wz2cool.dynamic.lambda.GetCommonPropertyFunction;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.github.wz2cool.dynamic.mybatis.mapper.batch.MapperBatchAction;
import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;
import com.innodealing.onshore.bondmetadata.enums.QuarterEnum;
import com.innodealing.onshore.bondservice.config.datasource.PgBondDataSourceConfig;
import com.innodealing.onshore.bondservice.mapper.pgbond.PgOnshoreBondCashDetailGroupMapper;
import com.innodealing.onshore.bondservice.mapper.pgbond.PgOnshoreBondCashDetailMapper;
import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import com.innodealing.onshore.bondservice.model.dto.request.BondCashDistributionDetailPageRequestDTO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.PgOnshoreBondCashDetailDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.OnshoreBondCashDetailGroupDO;
import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;
import com.innodealing.onshore.bondservice.service.internal.OnshoreUidService;
import com.innodealing.onshore.bondservice.utils.CamelCaseUtils;
import jodd.util.StringPool;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 国内债券现金流水明细表表数据库访问层 {@link PgOnshoreBondCashDetailDO}
 * 对GpOnshoreBondCashDetailMapper层做出简单封装 {@link PgOnshoreBondCashDetailMapper}
 *
 * <AUTHOR>
 */
@Repository
public class PgOnshoreBondCashDetailDAO {

    @Resource
    private PgOnshoreBondCashDetailMapper pgOnshoreBondCashDetailMapper;
    @Resource
    private PgOnshoreBondCashDetailGroupMapper pgOnshoreBondCashDetailGroupMapper;
    @Resource(name = PgBondDataSourceConfig.SESSION_FACTORY_NAME)
    private SqlSessionFactory sqlSessionFactory;
    @Resource
    private OnshoreUidService onshoreUidService;
    private static final String SERVICE_KEY = "onshore-bond-service:onshore_bond_cash_detail";


    /**
     * 获取债券现金流水明细表
     *
     * @param req {@link BondCashDistributionDetailPageRequestDTO}
     * @return {@link NormPagingResult }<{@link PgOnshoreBondCashDetailDO }>
     */
    public NormPagingResult<PgOnshoreBondCashDetailDO> getOnshoreBondCashDetailPaging(BondCashDistributionDetailPageRequestDTO req) {
        NormPagingQuery<PgOnshoreBondCashDetailDO> normPagingQuery = NormPagingQuery.createQuery(
                        PgOnshoreBondCashDetailDO.class, req.getPageNum(), req.getPageSize(), false, true)
                .select(getDefaultPropertyFunctions())
                .and(PgOnshoreBondCashDetailDO::getComUniCode, v -> v.in(req.getComUniCodes()))
                .and(CollectionUtils.isNotEmpty(req.getBondUniCodes()), PgOnshoreBondCashDetailDO::getBondUniCode, v -> v.in(req.getBondUniCodes()))
                .and(Objects.nonNull(req.getStartDate()), PgOnshoreBondCashDetailDO::getDataDate, v -> v.greaterThanOrEqual(req.getStartDate()))
                .and(Objects.nonNull(req.getEndDate()), PgOnshoreBondCashDetailDO::getDataDate, v -> v.lessThanOrEqual(req.getEndDate()));
        CustomSortDescriptor customSortDescriptor = new CustomSortDescriptor();
        if (StringUtils.isBlank(req.getSortProperty())) {
            customSortDescriptor.setExpression(String.format("%s %s NULLS LAST",  CamelCaseUtils.convertHumpToUnderLine("theoryInterestDate"), SortDirection.DESC.name()));
        } else {
            customSortDescriptor.setExpression(String.format("%s %s NULLS LAST", CamelCaseUtils.convertHumpToUnderLine(req.getSortProperty()), req.getSortDirection().name()));
        }
        normPagingQuery.addSorts(customSortDescriptor);
        return pgOnshoreBondCashDetailMapper.selectByNormalPaging(normPagingQuery);
    }

    /**
     * 批量插入或更新
     *
     * @param gpOnshoreBondCashDetails List<GpOnshoreBondCashDetailDO>
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = PgBondDataSourceConfig.TRANSACTION_NAME)
    public int batchUpsert(List<PgOnshoreBondCashDetailDO> gpOnshoreBondCashDetails) {
        List<Long> uids = onshoreUidService.listUid(SERVICE_KEY, gpOnshoreBondCashDetails.size());
        if (CollectionUtils.isEmpty(gpOnshoreBondCashDetails)) {
            return 0;
        }
        FilterGroupDescriptor<PgOnshoreBondCashDetailDO> filterGroup = new FilterGroupDescriptor<>();
        for (PgOnshoreBondCashDetailDO gpOnshoreBondCashDetailDO : gpOnshoreBondCashDetails) {
            FilterGroupDescriptor<PgOnshoreBondCashDetailDO> innerFilterGroup = new FilterGroupDescriptor<>();
            innerFilterGroup.and(PgOnshoreBondCashDetailDO::getComUniCode, v -> v.isEqual(gpOnshoreBondCashDetailDO.getComUniCode()))
                    .and(PgOnshoreBondCashDetailDO::getBondUniCode, v -> v.isEqual(gpOnshoreBondCashDetailDO.getBondUniCode()))
                    .and(PgOnshoreBondCashDetailDO::getDataDate, v -> v.isEqual(gpOnshoreBondCashDetailDO.getDataDate()));
            filterGroup.or(innerFilterGroup);
        }
        DynamicQuery<PgOnshoreBondCashDetailDO> query = DynamicQuery.createQuery(PgOnshoreBondCashDetailDO.class).and(filterGroup);
        List<PgOnshoreBondCashDetailDO> olds = pgOnshoreBondCashDetailMapper.selectByDynamicQuery(query);
        Map<String, PgOnshoreBondCashDetailDO> oldMap = olds.stream()
                .collect(Collectors.toMap(this::getUniqueKey, Function.identity(), (v1, v2) -> v2));
        Set<String> keys = oldMap.keySet();
        List<PgOnshoreBondCashDetailDO> insertList = new ArrayList<>();
        List<PgOnshoreBondCashDetailDO> updateList = new ArrayList<>();
        int idIndex = 0;
        for (PgOnshoreBondCashDetailDO entity : gpOnshoreBondCashDetails) {
            String key = this.getUniqueKey(entity);
            if (keys.contains(key)) {
                entity.setId(oldMap.get(key).getId());
                updateList.add(entity);
            } else {
                entity.setId(uids.get(idIndex));
                insertList.add(entity);
            }
            idIndex++;

        }
        return this.batchInsertSelective(insertList) + this.batchUpdateByIdSelective(updateList);
    }


    /**
     * 批量插入
     *
     * @param assetDayNewestQuotes List<GpOnshoreBondCashDetailDO>
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = PgBondDataSourceConfig.TRANSACTION_NAME)
    public int batchInsertSelective(List<PgOnshoreBondCashDetailDO> assetDayNewestQuotes) {
        if (CollectionUtils.isEmpty(assetDayNewestQuotes)) {
            return 0;
        }
        MapperBatchAction<PgOnshoreBondCashDetailMapper> insertBatchAction = MapperBatchAction.create(PgOnshoreBondCashDetailMapper.class, sqlSessionFactory);
        for (PgOnshoreBondCashDetailDO entity : assetDayNewestQuotes) {
            insertBatchAction.addAction(mapper -> mapper.insertSelective(entity));
        }
        return insertBatchAction.doBatchActions();
    }

    /**
     * 批量更新
     *
     * @param assetDayNewestQuotes List<GpOnshoreBondCashDetailDO>
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = PgBondDataSourceConfig.TRANSACTION_NAME)
    public int batchUpdateByIdSelective(List<PgOnshoreBondCashDetailDO> assetDayNewestQuotes) {
        if (CollectionUtils.isEmpty(assetDayNewestQuotes)) {
            return 0;
        }
        MapperBatchAction<PgOnshoreBondCashDetailMapper> insertBatchAction = MapperBatchAction.create(PgOnshoreBondCashDetailMapper.class, sqlSessionFactory);
        for (PgOnshoreBondCashDetailDO entity : assetDayNewestQuotes) {
            insertBatchAction.addAction(mapper -> mapper.updateByPrimaryKeySelective(entity));
        }
        return insertBatchAction.doBatchActions();
    }


    public List<BondCashPayInfoDTO> listBondCashPayInfoByTime(Collection<Long> bondUniCodes,
                                                              DateTypeEnum dateType,
                                                              Integer exerciseStatus) {
        if (CollectionUtils.isEmpty(bondUniCodes)) {
            return Collections.emptyList();
        }
        GetGroupFieldPropertyFunction[] yearGroupPropertyFunctions = Objects.equals(dateType, DateTypeEnum.YEAR) ?
                getYearGroupPropertyFunctions() : Objects.equals(dateType, DateTypeEnum.QUARTER)
                ?
                getYearQuarterGroupPropertyFunctions() : getYearMonthGroupPropertyFunctions();
        GroupedQuery<PgOnshoreBondCashDetailDO, OnshoreBondCashDetailGroupDO> query = GroupByQuery.createQuery(PgOnshoreBondCashDetailDO.class, OnshoreBondCashDetailGroupDO.class)
                .select(yearGroupPropertyFunctions)
                .and(PgOnshoreBondCashDetailDO::getBondUniCode, v -> v.in(bondUniCodes))
                .groupBy(this.getTimeDimensionPropertyFunctions(dateType));
        return pgOnshoreBondCashDetailGroupMapper.selectByGroupedQuery(query)
                .stream()
                .map(data -> {
                    BondCashPayInfoDTO dto = new BondCashPayInfoDTO();
                    dto.setDataYear(data.getDataYear());
                    dto.setDataMonth(data.getDataMonth());
                    dto.setDataQuarter(data.getDataQuarter());
                    String dataStr = Objects.equals(dateType, DateTypeEnum.YEAR) ?
                            Objects.toString(data.getDataYear()) : Objects.equals(dateType, DateTypeEnum.QUARTER)
                            ?
                            data.getDataYear() + StringPool.DASH + ITextValueEnum.getEnum(QuarterEnum.class, data.getDataQuarter()) : String.format("%d-%02d", data.getDataYear(), data.getDataMonth());
                    dto.setDataDateStr(dataStr);
                    dto.setPayInterestCash(Objects.equals(exerciseStatus, 0) ? data.getTotalPayInterestCash() : data.getTotalExercisePayInterestCash());
                    dto.setPayPrincipalCash(Objects.equals(exerciseStatus, 0) ? data.getTotalPayPrincipalCash() : data.getTotalExercisePayPrincipalCash());
                    return dto;
                })
                .collect(Collectors.toList());
    }


    /**
     * 获取unique主键
     *
     * @param comUniCode  主体编码
     * @param bondUniCode 债券唯一编码
     * @param dataYear    数据year
     * @param dataMonth   数据month
     * @return {@link String}
     */
    public String getUniqueKey(Long comUniCode, Long bondUniCode, Integer dataYear, Integer dataMonth) {
        return comUniCode + "_" + bondUniCode + "_" + dataYear + "_" + dataMonth;
    }

    /**
     * 删除所有数据
     * 影响行数
     */
    @Transactional(transactionManager = PgBondDataSourceConfig.TRANSACTION_NAME, rollbackFor = Exception.class)
    public void deleteAll() {
        pgOnshoreBondCashDetailMapper.deleteByDynamicQuery(DynamicQuery.createQuery(PgOnshoreBondCashDetailDO.class));
    }

    /**
     * 获取unique主键
     *
     * @param cashDetail 现金流明细
     * @return {@link String}
     */
    public String getUniqueKey(PgOnshoreBondCashDetailDO cashDetail) {
        return cashDetail.getComUniCode() + "_" + cashDetail.getBondUniCode() + "_" + cashDetail.getDataDate();
    }

    private GetGroupFieldPropertyFunction[] getYearGroupPropertyFunctions() {
        return new GetGroupFieldPropertyFunction[]{
                OnshoreBondCashDetailGroupDO::getDataYear,
                OnshoreBondCashDetailGroupDO::getTotalExercisePayInterestCash,
                OnshoreBondCashDetailGroupDO::getTotalExercisePayPrincipalCash,
                OnshoreBondCashDetailGroupDO::getTotalPayInterestCash,
                OnshoreBondCashDetailGroupDO::getTotalPayPrincipalCash
        };
    }

    private GetGroupFieldPropertyFunction[] getYearMonthGroupPropertyFunctions() {
        return new GetGroupFieldPropertyFunction[]{
                OnshoreBondCashDetailGroupDO::getDataYear,
                OnshoreBondCashDetailGroupDO::getDataMonth,
                OnshoreBondCashDetailGroupDO::getTotalExercisePayInterestCash,
                OnshoreBondCashDetailGroupDO::getTotalExercisePayPrincipalCash,
                OnshoreBondCashDetailGroupDO::getTotalPayInterestCash,
                OnshoreBondCashDetailGroupDO::getTotalPayPrincipalCash
        };
    }

    private GetGroupFieldPropertyFunction[] getYearQuarterGroupPropertyFunctions() {
        return new GetGroupFieldPropertyFunction[]{
                OnshoreBondCashDetailGroupDO::getDataYear,
                OnshoreBondCashDetailGroupDO::getDataQuarter,
                OnshoreBondCashDetailGroupDO::getTotalExercisePayInterestCash,
                OnshoreBondCashDetailGroupDO::getTotalExercisePayPrincipalCash,
                OnshoreBondCashDetailGroupDO::getTotalPayInterestCash,
                OnshoreBondCashDetailGroupDO::getTotalPayPrincipalCash
        };
    }


    private GetCommonPropertyFunction<PgOnshoreBondCashDetailDO>[] getDefaultPropertyFunctions() {
        return new GetBasicFieldPropertyFunction[]{
                PgOnshoreBondCashDetailDO::getBondUniCode,
                PgOnshoreBondCashDetailDO::getComUniCode,
                PgOnshoreBondCashDetailDO::getDataDate,
                PgOnshoreBondCashDetailDO::getDataYear,
                PgOnshoreBondCashDetailDO::getDataQuarter,
                PgOnshoreBondCashDetailDO::getDataMonth,
                PgOnshoreBondCashDetailDO::getExercisePayInterestCash,
                PgOnshoreBondCashDetailDO::getExercisePayPrincipalCash,
                PgOnshoreBondCashDetailDO::getPayInterestCash,
                PgOnshoreBondCashDetailDO::getPayPrincipalCash,
                PgOnshoreBondCashDetailDO::getTheoryInterestDate
        };
    }

    private GetCommonPropertyFunction<PgOnshoreBondCashDetailDO>[] getTimeDimensionPropertyFunctions(DateTypeEnum dataType) {
        return Objects.equals(dataType, DateTypeEnum.YEAR)
                ? new GetBasicFieldPropertyFunction[]{PgOnshoreBondCashDetailDO::getDataYear}
                : Objects.equals(dataType, DateTypeEnum.QUARTER)
                ? new GetBasicFieldPropertyFunction[]{PgOnshoreBondCashDetailDO::getDataYear, PgOnshoreBondCashDetailDO::getDataQuarter}
                : new GetBasicFieldPropertyFunction[]{PgOnshoreBondCashDetailDO::getDataYear, PgOnshoreBondCashDetailDO::getDataMonth};
    }


    interface GetBasicFieldPropertyFunction extends GetCommonPropertyFunction<PgOnshoreBondCashDetailDO> {
    }

    interface GetGroupFieldPropertyFunction extends GetCommonPropertyFunction<OnshoreBondCashDetailGroupDO> {
    }
}
