package com.innodealing.onshore.bondservice.model.enums;

import com.innodealing.onshore.bondmetadata.enums.ITextValueEnum;

import java.util.Arrays;
import java.util.List;

/**
 * abs 当事人类型
 *
 * <AUTHOR>
 */
public enum AbsPartyTypeEnum implements ITextValueEnum {

    PLAN_MANAGER(1, "计划管理人"),
    ORIGINAL_STAKEHOLDER(2, "原始权益人"),
    LEAD_UNDERWRITER(3, "主承销商"),
    ACTUAL_DEBTOR(4, "实际债务人"),
    JOINT_DEBTOR(5, "共同债务人"),
    SIGNIFICANT_DEBTOR(6, "重要债务人"),
    UNDERLYING_DEBTOR(7, "基础债务人"),
    DIFFERENCE_PAYMENT_PROMISOR(8, "差额支付承诺人"),
    LIQUIDITY_SUPPORT_INSTITUTION(9, "流动性支持机构"),
    GUARANTEE(10, "担保人"),
    CREDIT_ENHANCEMENT_INSTITUTION(11, "信用增级机构"),
    REP<PERSON>CHASER(12, "回购人"),
    LOAN_INSURER(13, "贷款保险人"),
    GUARANTOR(14, "保证人");

    private final int value;
    private final String text;

    AbsPartyTypeEnum(int value, String text) {
        this.value = value;
        this.text = text;
    }

    @Override
    public String getText() {
        return text;
    }

    @Override
    public int getValue() {
        return value;
    }

    /**
     * 获取信用主体类型
     *
     * @return 信用主体的类型
     */
    public static List<AbsPartyTypeEnum> listCreditSubjectTypeEnum() {
        return Arrays.asList(ACTUAL_DEBTOR,
                JOINT_DEBTOR,
                SIGNIFICANT_DEBTOR,
                UNDERLYING_DEBTOR,
                DIFFERENCE_PAYMENT_PROMISOR,
                LIQUIDITY_SUPPORT_INSTITUTION,
                GUARANTEE,
                CREDIT_ENHANCEMENT_INSTITUTION,
                REPURCHASER,
                LOAN_INSURER,
                GUARANTOR);
    }
}
