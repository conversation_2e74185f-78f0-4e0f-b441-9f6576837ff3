package com.innodealing.onshore.bondservice.service.consumer;

import com.fasterxml.jackson.core.type.TypeReference;
import com.innodealing.commons.json.JSON;
import com.innodealing.commons.mq.MqMessageUtils;
import com.innodealing.kafka.annotation.HermesKafkaListener;
import com.innodealing.kafka.listener.HermesBatchOrderlyListener;
import com.innodealing.kafka.model.KafkaMessage;
import com.innodealing.onshore.bondmetadata.bo.rocketmq.CanalFlatMessageBO;
import com.innodealing.onshore.bondmetadata.exception.BusinessException;
import com.innodealing.onshore.bondservice.config.constant.KafkaConstant;
import com.innodealing.onshore.bondservice.model.dto.DwdCbValuationMqDTO;
import com.innodealing.onshore.bondservice.service.BondFilterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 消费中债估值 dwd
 *
 * <AUTHOR>
 * @date 2024/10/23 14:38
 */
@Service
@HermesKafkaListener(topics = "${POLARDB_PROFILES_ACTIVE:}" + KafkaConstant.DWD_CHINA_BOND_DWD_CB_VALUATION_TOPIC,
        groupId = "${POLARDB_PROFILES_ACTIVE:}" + KafkaConstant.KAFKA_DEFAULT_GROUP + "-cb-onshore_bond_filter")
public class DwdCbValuationConsumer extends MqBaseConsumer implements HermesBatchOrderlyListener {
    private Logger logger = LoggerFactory.getLogger(this.getClass());
    @Resource
    private BondFilterService bondFilterService;

    @Override
    public void onMessage(List<KafkaMessage> kafkaMessageList) {
        if (this.getMqSkipStatus()) {
            return;
        }
        String messageBodyJson = JSON.toJSONString(kafkaMessageList);
        try {
            List<CanalFlatMessageBO<DwdCbValuationMqDTO>> canalFlatMessageList = new ArrayList<>();
            for (KafkaMessage kafkaMessage : kafkaMessageList) {
                MqMessageUtils.parseMessage(kafkaMessage.getMessageKey(), kafkaMessage.getContent(), new TypeReference<CanalFlatMessageBO<DwdCbValuationMqDTO>>() {
                }).ifPresent(mqMessage -> canalFlatMessageList.add(mqMessage.getMessageBody()));
            }
            bondFilterService.syncDwdCbValuationByMq(canalFlatMessageList);
        } catch (Throwable ex) {
            logger.error(String.format("DwdCbValuationConsumer.consumeMessage error message: %s ", messageBodyJson), ex);
            throw new BusinessException(JSON.toJSONString(ex));
        }
    }
}
