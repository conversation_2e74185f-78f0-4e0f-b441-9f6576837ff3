package com.innodealing.onshore.bondservice.service;

import com.innodealing.onshore.bondservice.model.dto.BondCashPayInfoDTO;
import com.innodealing.onshore.bondservice.model.enums.DateTypeEnum;

import java.util.List;
import java.util.Set;

/**
 * 债券数据查询策略接口
 *
 * <AUTHOR>
 * @date 2025/06/23
 */
public interface MvOnshoreBondCashDetailStrategy {

    /**
     * 获取当前策略支持的时间类型
     *
     * @return 支持的时间类型枚举
     */
    DateTypeEnum getSupportedDateType();

    /**
     * 查询当前企业数据非行权
     *
     * @param comUniCodes 企业统一编码集合
     * @return 债券现金流信息列表
     */
    List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataNoExercise(Set<Long> comUniCodes);


    /**
     * 查询当前企业数据行权
     *
     * @param comUniCodes 企业统一编码集合
     * @return 债券现金流信息列表
     */
    List<BondCashPayInfoDTO> queryMvOnshoreBondCashDetailDataExercise(Set<Long> comUniCodes);


}