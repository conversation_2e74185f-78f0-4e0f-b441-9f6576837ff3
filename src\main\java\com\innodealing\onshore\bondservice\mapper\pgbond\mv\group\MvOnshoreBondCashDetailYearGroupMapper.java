package com.innodealing.onshore.bondservice.mapper.pgbond.mv.group;

import com.github.wz2cool.dynamic.mybatis.mapper.SelectByGroupedQueryMapper;
import com.innodealing.onshore.bondservice.model.entity.pgbond.group.MvOnshoreBondCashDetailYearGroupDO;
import com.innodealing.onshore.bondservice.model.entity.pgbond.mv.MvOnshoreBondCashDetailYearDO;

/**
 * 国内债券现金流按年统计物化视图
 *
 * <AUTHOR>
 */
public interface MvOnshoreBondCashDetailYearGroupMapper extends SelectByGroupedQueryMapper<MvOnshoreBondCashDetailYearDO, MvOnshoreBondCashDetailYearGroupDO> {
}