package com.innodealing.onshore.bondservice.utils;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.innodealing.onshore.bondmetadata.utils.SyncDataUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.ibatis.binding.MapperProxy;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

/**
 * action工具类
 *
 * <AUTHOR>
 * @date 2022/6/20 10:50
 */
public final class MapperBatchActionUtil {
    private static final Logger LOGGER = LoggerFactory.getLogger(MapperBatchActionUtil.class);
    private static final String MAPPER_PROXY_FILED_IN_DYNAMIC_MAPPER = "h";
    private static final String MAPPER_INTERFACE_FILED_IN_MAPPER_PROXY = "mapperInterface";
    private static final String SQL_SESSION_FILED_IN_MAPPER_PROXY = "sqlSession";
    private static final LoadingCache<DynamicQueryMapper<?>, SessionMapper<?>> CLASS_SQL_SESSION_FACTORY_CACHE = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(500)
            .build(MapperBatchActionUtil::extraClassSqlSessionFactory);

    private MapperBatchActionUtil() {
    }

    /**
     * 批量新增,使用ExecutorType.Batch模式,
     * 注意：使用该模式返回的自增id只有最后一条
     *
     * @param dynamicMapper    dynamicMapper
     * @param insertCollection 插入实体集合
     * @return int 成功条数
     * <AUTHOR>
     * @date 2022/6/20 10:51
     */
    public static <M extends DynamicQueryMapper<T>, T> int batchInsertSelective(M dynamicMapper, Collection<T> insertCollection) {
        if (CollectionUtils.isEmpty(insertCollection)) {
            return 0;
        }
        List<Consumer<M>> actions = Lists.newArrayList();
        for (T t : insertCollection) {
            actions.add(x -> x.insertSelective(t));
        }
        return doBatchActions(dynamicMapper, actions);
    }

    /**
     * 批量操作,使用ExecutorType.Batch模式,
     * 注意：使用该模式返回的自增id只有最后一条
     *
     * @param dynamicMapper dynamicMapper
     * @param actions       操作集合
     * @return int 成功条数
     * <AUTHOR>
     * @date 2022/6/20 10:51
     */
    public static <M extends DynamicQueryMapper<T>, T> int doBatchActions(M dynamicMapper, List<Consumer<M>> actions) {
        final List<BatchResult> batchResults = doBatchActionWithResults(dynamicMapper, actions);
        return batchResults.stream().mapToInt(x -> {
            int effectRows = Arrays.stream(x.getUpdateCounts()).sum();
            if (effectRows > 0) {
                return effectRows;
            }
            return x.getParameterObjects().size();
        }).sum();
    }


    private static <M extends DynamicQueryMapper<T>, T> List<BatchResult> doBatchActionWithResults(M dynamicMapper, List<Consumer<M>> actions) {
        SessionMapper<M> mSessionMapper = (SessionMapper<M>) CLASS_SQL_SESSION_FACTORY_CACHE.get(dynamicMapper);
        Objects.requireNonNull(mSessionMapper);
        Class<M> mapperClass = mSessionMapper.mapperClass;
        SqlSessionFactory sqlSessionFactory = mSessionMapper.sqlSessionFactory;
        List<BatchResult> result = new ArrayList<>();
        int defaultPageSize = SyncDataUtil.DEFAULT_PAGE_SIZE_500;
        try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH, false)) {
            final M mapper = sqlSession.getMapper(mapperClass);
            for (int i = 0; i < actions.size(); i++) {
                actions.get(i).accept(mapper);
                if (i != 0 && (i + 1) % defaultPageSize == 0) {
                    LOGGER.info("flushStatements mapperClass:{} ,totalSize:{}, batchSize:{}, currentIndex:{}", mapperClass.getSimpleName(), actions.size(), defaultPageSize, i + 1);
                    final List<BatchResult> batchResults = sqlSession.flushStatements();
                    result.addAll(batchResults);
                }
            }
            final List<BatchResult> batchResults = sqlSession.flushStatements();
            result.addAll(batchResults);
        }
        LOGGER.info("flushStatements end mapperClass:{} ,totalSize:{}", mapperClass.getSimpleName(), actions.size());
        return result;
    }

    private static <M extends DynamicQueryMapper<T>, T> SessionMapper<M> extraClassSqlSessionFactory(M dynamicMapper) {
        try {
            Field mapperHead = ReflectionUtils.findField(dynamicMapper.getClass(), MAPPER_PROXY_FILED_IN_DYNAMIC_MAPPER);
            Objects.requireNonNull(mapperHead);
            mapperHead.setAccessible(true);
            MapperProxy<M> mapperProxy = (MapperProxy<M>) mapperHead.get(dynamicMapper);
            Field mapperInterface = ReflectionUtils.findField(mapperProxy.getClass(), MAPPER_INTERFACE_FILED_IN_MAPPER_PROXY);
            Objects.requireNonNull(mapperInterface);
            mapperInterface.setAccessible(true);
            Class<M> mapperClass = (Class<M>) mapperInterface.get(mapperProxy);
            Field sqlSession = ReflectionUtils.findField(mapperProxy.getClass(), SQL_SESSION_FILED_IN_MAPPER_PROXY);
            Objects.requireNonNull(sqlSession);
            sqlSession.setAccessible(true);
            SqlSessionTemplate sqlSessionTemplate = (SqlSessionTemplate) sqlSession.get(mapperProxy);
            SqlSessionFactory sqlSessionFactory = sqlSessionTemplate.getSqlSessionFactory();
            return new SessionMapper<>(sqlSessionFactory, mapperClass);
        } catch (IllegalAccessException e) {
            throw new IllegalArgumentException(e);
        }
    }

    private static class SessionMapper<M> {
        private final SqlSessionFactory sqlSessionFactory;
        private final Class<M> mapperClass;

        public SessionMapper(SqlSessionFactory sqlSessionFactory, Class<M> mapperClass) {
            this.sqlSessionFactory = sqlSessionFactory;
            this.mapperClass = mapperClass;
        }
    }
}



